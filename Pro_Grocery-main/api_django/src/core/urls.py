"""
URL configuration for core project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.http import HttpResponse
from django.shortcuts import redirect
from django.urls import path, include
from ninja import NinjaAPI

from .api_docs import register_docs_endpoints

# Import API routers
from core.api import router as core_router
from accounts.api.custom_user import router as users_router
from accounts.api.wallet import router as wallets_router
from accounts.api.transaction import router as transactions_router
from accounts.api.otp import router as otps_router
from accounts.api.notification import router as notifications_router
from accounts.api.account_verification_id import router as account_verifications_router
from products.api.product import router as products_router
from products.api.category import router as categories_router
from products.api.company import router as companies_router
from products.api.region import router as regions_router
from stores.api.store import router as stores_router
from stores.api.order import router as orders_router
from wholesalers.api.wholesaler import router as wholesalers_router
from wholesalers.api.min_charge import router as min_charges_router
from wholesalers.api.items import router as items_router

# Import HTML views


def index(request):
    return HttpResponse("Hello, World!")


api = NinjaAPI()

# Register core API endpoints
api.add_router("", core_router)

# Register accounts API endpoints
api.add_router("users/", users_router)
# api.add_router("wallets/", wallets_router)
# api.add_router("transactions/", transactions_router)
# api.add_router("otps/", otps_router)
api.add_router("notifications/", notifications_router)
# api.add_router("account-verifications/", account_verifications_router)

# Register products API endpoints
api.add_router("products/", products_router)
api.add_router("categories/", categories_router)
api.add_router("companies/", companies_router)
api.add_router("regions/", regions_router)

# Register stores API endpoints
api.add_router("stores/", stores_router)
api.add_router("orders/", orders_router)

# Register wholesalers API endpoints
api.add_router("wholesalers/", wholesalers_router)
api.add_router("min-charges/", min_charges_router)
api.add_router("items/", items_router)

# Register documentation endpoints
# register_docs_endpoints(api)


@api.get("/")
def test(request):
    # test redis
    from django_redis import get_redis_connection

    redis = get_redis_connection("default")
    redis.set("test", "test")
    return {"message": "Hello, World!"}


def index_view(request):
    if request.get_host() == "gomla.tagerplus.com":
        return redirect("wholesaler_dashboard")


urlpatterns = [
    path("", index_view, name="index"),
    path("admin/", admin.site.urls),
    path("api/", api.urls),
    # Wholesaler Dashboard
    path("wholesaler/", include("wholesalers.web_urls")),
    # path("api/v2/", .urls),
    # path("", index_view, name="index"),
    # # HTML views for companies
    # path("companies/", company_list_view, name="company_list"),
    # path("companies/create/", company_create_view, name="company_create"),
    # # HTML views for categories
    # path("categories/", category_list_view, name="category_list"),
    # path("categories/create/", category_create_view, name="category_create"),
    # path("categories/<int:category_id>/", category_detail_view, name="category_detail"),
    # # HTML views for products
    # path("products/", product_list_view, name="product_list"),
    # path("products/create/", product_create_view, name="product_create"),
    # path("products/<int:product_id>/", product_detail_view, name="product_detail"),
    # path(
    #     "products/<int:product_id>/update/", product_update_view, name="product_update"
    # ),
]
