"""
Tests for the Product API endpoints.
"""

from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from django.test import TestCase
from django.utils import timezone
from ninja.testing import TestClient
from unittest.mock import patch, MagicMock

from accounts.models import CustomUser
from products.models import Product, Company, Category, Region
from wholesalers.models import Wholesaler, Item, RegionMinCharge
from products.api.product import router
from api.utils import generate_jwt_token


class ProductAPITestCase(TestCase):
    def setUp(self):
        self.client = TestClient(router)

        # Create test user
        self.user = CustomUser.objects.create_user(
            phone_number="+**********", first_name="Test", last_name="User"
        )

        # Generate JWT token for authentication
        self.token = generate_jwt_token(self.user)
        self.auth_headers = {"Authorization": f"Bearer {self.token}"}

        # Create test data
        self.company = Company.objects.create(
            name="Test Company", title="Test Company Title", slug="test-company"
        )

        self.category = Category.objects.create(
            name="Test Category", title="Test Category Title", slug="test-category"
        )

        # Create regions (country -> state -> city hierarchy)
        self.country = Region.objects.create(
            name="Test Country", type="COUNTRY", slug="test-country"
        )

        self.state = Region.objects.create(
            name="Test State", type="STATE", parent=self.country, slug="test-state"
        )

        self.city = Region.objects.create(
            name="Test City", type="CITY", parent=self.state, slug="test-city"
        )

        # Create product
        self.product = Product.objects.create(
            name="Test Product",
            title="Test Product Title",
            barcode="**********123",
            slug="test-product",
            description="Test Description",
            company=self.company,
            category=self.category,
            unit="PIECE",
            unit_count=Decimal("1.0"),
            items_count=2,
        )

        # Create wholesaler user
        self.wholesaler_user = CustomUser.objects.create_user(
            phone_number="+1234567891", first_name="Wholesaler", last_name="User"
        )

        # Create wholesaler
        self.wholesaler = Wholesaler.objects.create(
            category="GROCERY",
            user=self.wholesaler_user,
            title="Test Wholesaler",
            username="test_wholesaler",
        )

        # Create regional minimum charge
        self.region_min_charge = RegionMinCharge.objects.create(
            wholesaler=self.wholesaler,
            region=self.city,
            min_charge=Decimal("100.00"),
            min_items=5,
        )

        # Create wholesaler item
        self.item = Item.objects.create(
            wholesaler=self.wholesaler,
            product=self.product,
            base_price=Decimal("25.50"),
            inventory_count=100,
            minimum_order_quantity=1,
            maximum_order_quantity=50,
            price_expiry=timezone.now() + timedelta(days=3),
            expires_at=timezone.now() + timedelta(days=3),
        )

    def test_get_product_with_wholesaler_items_success(self):
        """Test successful retrieval of product with wholesaler items"""
        response = self.client.post(
            f"/{self.product.id}/wholesaler-items",
            json={"region_id": self.city.id},
            headers=self.auth_headers,
        )

        self.assertEqual(response.status_code, 200)
        data = response.json()

        # Check product details
        self.assertEqual(data["id"], self.product.id)
        self.assertEqual(data["name"], self.product.name)
        self.assertEqual(data["title"], self.product.title)
        self.assertEqual(data["barcode"], self.product.barcode)

        # Check company and category
        self.assertEqual(data["company"]["id"], self.company.id)
        self.assertEqual(data["category"]["id"], self.category.id)

        # Check wholesaler items
        self.assertEqual(len(data["wholesaler_items"]), 1)
        wholesaler_item = data["wholesaler_items"][0]

        # Check item details
        self.assertEqual(wholesaler_item["item"]["id"], self.item.id)
        self.assertEqual(
            float(wholesaler_item["item"]["base_price"]), float(self.item.base_price)
        )
        self.assertEqual(
            wholesaler_item["item"]["inventory_count"], self.item.inventory_count
        )

        # Check wholesaler details
        self.assertEqual(wholesaler_item["wholesaler"]["id"], self.wholesaler.id)
        self.assertEqual(wholesaler_item["wholesaler"]["title"], self.wholesaler.title)

        # Check regional pricing
        self.assertIsNotNone(wholesaler_item["regional_pricing"])
        self.assertEqual(
            float(wholesaler_item["regional_pricing"]["min_charge"]),
            float(self.region_min_charge.min_charge),
        )
        self.assertEqual(
            wholesaler_item["regional_pricing"]["min_items"],
            self.region_min_charge.min_items,
        )

    def test_get_product_with_wholesaler_items_invalid_product(self):
        """Test retrieval with invalid product ID"""
        response = self.client.post(
            "/99999/wholesaler-items",
            json={"region_id": self.city.id},
            headers=self.auth_headers,
        )

        self.assertEqual(response.status_code, 404)

    def test_get_product_with_wholesaler_items_invalid_region(self):
        """Test retrieval with invalid region ID"""
        response = self.client.post(
            f"/{self.product.id}/wholesaler-items",
            json={"region_id": 99999},
            headers=self.auth_headers,
        )

        self.assertEqual(response.status_code, 400)

    def test_get_product_with_wholesaler_items_no_auth(self):
        """Test retrieval without authentication"""
        response = self.client.post(
            f"/{self.product.id}/wholesaler-items", json={"region_id": self.city.id}
        )

        self.assertEqual(response.status_code, 401)

    def test_get_product_with_wholesaler_items_region_hierarchy(self):
        """Test that regional hierarchy works correctly"""
        # Create another wholesaler that serves the state level
        state_wholesaler_user = CustomUser.objects.create_user(
            phone_number="+1234567892", first_name="State Wholesaler", last_name="User"
        )

        state_wholesaler = Wholesaler.objects.create(
            category="GROCERY",
            user=state_wholesaler_user,
            title="State Wholesaler",
            username="state_wholesaler",
        )

        # Create regional minimum charge for state
        RegionMinCharge.objects.create(
            wholesaler=state_wholesaler,
            region=self.state,
            min_charge=Decimal("200.00"),
            min_items=10,
        )

        # Create item for state wholesaler
        Item.objects.create(
            wholesaler=state_wholesaler,
            product=self.product,
            base_price=Decimal("30.00"),
            inventory_count=50,
            minimum_order_quantity=2,
            price_expiry=timezone.now() + timedelta(days=3),
            expires_at=timezone.now() + timedelta(days=3),
        )

        # Request with city region should return both wholesalers (city and state)
        response = self.client.post(
            f"/{self.product.id}/wholesaler-items",
            json={"region_id": self.city.id},
            headers=self.auth_headers,
        )

        self.assertEqual(response.status_code, 200)
        data = response.json()

        # Should have 2 wholesaler items (city and state level)
        self.assertEqual(len(data["wholesaler_items"]), 2)

        # Verify both wholesalers are present
        wholesaler_titles = [
            item["wholesaler"]["title"] for item in data["wholesaler_items"]
        ]
        self.assertIn("Test Wholesaler", wholesaler_titles)
        self.assertIn("State Wholesaler", wholesaler_titles)
