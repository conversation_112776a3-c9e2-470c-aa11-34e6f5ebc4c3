"""
Simple tests for the Product API endpoints that don't require pgvector.
"""

from datetime import datetime, timedelta
from decimal import Decimal
from django.test import TestCase, override_settings
from django.utils import timezone
from ninja.testing import TestClient
from unittest.mock import patch, MagicMock

from accounts.models import CustomUser
from products.models import Product, Company, Category, Region
from wholesalers.models import Wholesaler, Item, RegionMinCharge
from products.api.product import router
from api.utils import generate_jwt_token


# Use SQLite for testing to avoid pgvector dependency
@override_settings(
    DATABASES={
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': ':memory:',
        }
    }
)
class SimpleProductAPITestCase(TestCase):
    def setUp(self):
        self.client = TestClient(router)
        
        # Create test user
        self.user = CustomUser.objects.create_user(
            phone_number="+**********",
            first_name="Test",
            last_name="User"
        )
        
        # Generate JWT token for authentication
        self.token = generate_jwt_token(self.user)
        self.auth_headers = {"Authorization": f"Bearer {self.token}"}

    def test_endpoint_structure_validation(self):
        """Test that the endpoint accepts the correct input structure"""
        # This test validates the API structure without requiring full database setup
        
        # Mock the database calls to avoid pgvector issues
        with patch('products.api.product.get_object_or_404') as mock_get_object:
            with patch('products.api.product._get_region_hierarchy') as mock_hierarchy:
                with patch('products.api.product.Item.objects') as mock_items:
                    
                    # Setup mocks
                    mock_product = MagicMock()
                    mock_product.id = 1
                    mock_product.name = "Test Product"
                    mock_product.title = "Test Product Title"
                    mock_product.barcode = "**********123"
                    mock_product.slug = "test-product"
                    mock_product.description = "Test Description"
                    mock_product.unit = "PIECE"
                    mock_product.unit_count = Decimal("1.0")
                    mock_product.items_count = 1
                    mock_product.created_at = timezone.now()
                    mock_product.updated_at = timezone.now()
                    mock_product.image = None
                    mock_product.company = None
                    mock_product.category = None
                    
                    mock_get_object.return_value = mock_product
                    mock_hierarchy.return_value = [1, 2, 3]
                    
                    # Mock empty queryset for items
                    mock_queryset = MagicMock()
                    mock_queryset.__iter__ = lambda x: iter([])
                    mock_items.filter.return_value.select_related.return_value.prefetch_related.return_value.distinct.return_value = mock_queryset
                    
                    # Test the endpoint
                    response = self.client.post(
                        "/1/wholesaler-items",
                        json={"region_id": 1},
                        headers=self.auth_headers
                    )
                    
                    # Should return 200 with correct structure
                    self.assertEqual(response.status_code, 200)
                    data = response.json()
                    
                    # Validate response structure
                    self.assertIn("id", data)
                    self.assertIn("name", data)
                    self.assertIn("title", data)
                    self.assertIn("barcode", data)
                    self.assertIn("wholesaler_items", data)
                    self.assertEqual(data["id"], 1)
                    self.assertEqual(data["name"], "Test Product")
                    self.assertIsInstance(data["wholesaler_items"], list)

    def test_invalid_region_id(self):
        """Test that invalid region_id returns 400"""
        with patch('products.api.product.get_object_or_404') as mock_get_object:
            with patch('products.api.product._get_region_hierarchy') as mock_hierarchy:
                
                mock_product = MagicMock()
                mock_get_object.return_value = mock_product
                mock_hierarchy.return_value = []  # Empty list indicates invalid region
                
                response = self.client.post(
                    "/1/wholesaler-items",
                    json={"region_id": 99999},
                    headers=self.auth_headers
                )
                
                self.assertEqual(response.status_code, 400)

    def test_missing_authentication(self):
        """Test that missing authentication returns 401"""
        response = self.client.post(
            "/1/wholesaler-items",
            json={"region_id": 1}
        )
        
        self.assertEqual(response.status_code, 401)

    def test_invalid_product_id(self):
        """Test that invalid product_id returns 404"""
        from django.http import Http404
        
        with patch('products.api.product.get_object_or_404') as mock_get_object:
            mock_get_object.side_effect = Http404("Product not found")
            
            response = self.client.post(
                "/99999/wholesaler-items",
                json={"region_id": 1},
                headers=self.auth_headers
            )
            
            self.assertEqual(response.status_code, 404)

    def test_request_body_validation(self):
        """Test that request body validation works correctly"""
        # Test missing region_id
        response = self.client.post(
            "/1/wholesaler-items",
            json={},
            headers=self.auth_headers
        )
        
        self.assertEqual(response.status_code, 422)  # Validation error
        
        # Test invalid region_id type
        response = self.client.post(
            "/1/wholesaler-items",
            json={"region_id": "invalid"},
            headers=self.auth_headers
        )
        
        self.assertEqual(response.status_code, 422)  # Validation error
