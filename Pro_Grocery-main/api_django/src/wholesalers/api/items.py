from ninja import Router, Schema
from ninja.errors import HttpError
from typing import Optional, List
from decimal import Decimal
import logging

from django.core.paginator import Paginator
from django.db.models import Q

from api.middleware import AuthMiddleware
from wholesalers.models import Item

logger = logging.getLogger(__name__)

router = Router(tags=["items"])


# =============================================================================
# Schemas
# =============================================================================


class CategoryOut(Schema):
    id: int
    name: str
    title: str
    slug: str


class ItemProductOut(Schema):
    id: int
    name: str
    title: str
    barcode: str
    slug: str
    description: str
    image_url: Optional[str] = None
    category: Optional[CategoryOut] = None
    unit: str
    unit_count: float


class ItemOut(Schema):
    id: int
    wholesaler_id: int
    product: ItemProductOut
    base_price: float
    inventory_count: int
    minimum_order_quantity: int
    maximum_order_quantity: Optional[int] = None


class PaginatedItemsResponse(Schema):
    items: List[ItemOut]
    total_count: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool


# =============================================================================
# Endpoints
# =============================================================================


@router.get(
    "/wholesaler/{wholesaler_id}",
    response=PaginatedItemsResponse,
    auth=AuthMiddleware,
)
def list_wholesaler_items(
    request,
    wholesaler_id: int,
    page: int = 1,
    page_size: int = 20,
    search: Optional[str] = None,
    category_id: Optional[int] = None,
) -> PaginatedItemsResponse:
    """
    List items for a specific wholesaler including product and category data.
    Excludes items with zero inventory or zero price.
    """
    try:
        if page_size > 100:
            page_size = 100
        elif page_size < 1:
            page_size = 20

        queryset = (
            Item.objects.filter(
                wholesaler_id=wholesaler_id,
                deleted_at__isnull=True,
                inventory_count__gt=0,
                base_price__gt=0,
            )
            .select_related("product", "product__category", "wholesaler")
            .order_by("product__name")
        )

        if search:
            queryset = queryset.filter(
                Q(product__name__icontains=search)
                | Q(product__title__icontains=search)
                | Q(product__barcode__icontains=search)
                | Q(product__description__icontains=search)
            )

        if category_id:
            queryset = queryset.filter(product__category_id=category_id)

        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)

        items: List[ItemOut] = []
        for item in page_obj.object_list:
            product = item.product
            category = product.category
            items.append(
                ItemOut(
                    id=item.id,
                    wholesaler_id=item.wholesaler_id,
                    product=ItemProductOut(
                        id=product.id,
                        name=product.name,
                        title=product.title,
                        barcode=product.barcode,
                        slug=product.slug,
                        description=product.description,
                        image_url=product.image.url if product.image else None,
                        category=CategoryOut(
                            id=category.id,
                            name=category.name,
                            title=category.title,
                            slug=category.slug,
                        )
                        if category
                        else None,
                        unit=product.unit,
                        unit_count=float(product.unit_count),
                    ),
                    base_price=float(item.base_price),
                    inventory_count=item.inventory_count,
                    minimum_order_quantity=item.minimum_order_quantity,
                    maximum_order_quantity=item.maximum_order_quantity,
                )
            )

        return PaginatedItemsResponse(
            items=items,
            total_count=paginator.count,
            page=page,
            page_size=page_size,
            total_pages=paginator.num_pages,
            has_next=page_obj.has_next(),
            has_previous=page_obj.has_previous(),
        )

    except Exception as e:
        logger.exception(
            f"Error listing items for wholesaler {wholesaler_id}: {str(e)}"
        )
        raise HttpError(500, "Internal server error")
