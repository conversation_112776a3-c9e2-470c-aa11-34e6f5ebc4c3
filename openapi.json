{"openapi": "3.1.0", "info": {"title": "NinjaAPI", "version": "1.0.0", "description": ""}, "paths": {"/api/": {"get": {"operationId": "core_api_api_root", "summary": "Api Root", "parameters": [], "responses": {"200": {"description": "OK"}}, "description": "API root endpoint", "tags": ["core"]}}, "/api/signin": {"post": {"operationId": "core_api_signin", "summary": "Signin", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}}, "description": "User authentication endpoint\nReturns JWT token and user details including wholesaler_id if applicable", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}, "required": true}}}, "/api/signup": {"post": {"operationId": "core_api_signup", "summary": "Signup", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterResponse"}}}}}, "description": "User registration endpoint\nCreates user account with phone verification set to False", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}, "required": true}}}, "/api/me": {"get": {"operationId": "core_api_get_current_user", "summary": "Get Current User", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}}, "description": "Get the current authenticated user's data\nRequires authentication via JWT token", "tags": ["auth"], "security": [{"AuthBearerMiddleware": []}]}, "put": {"operationId": "core_api_update_current_user", "summary": "Update Current User", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}}, "description": "Update the current authenticated user's data\nRequires authentication via JWT token", "tags": ["auth"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateRequest"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/home/<USER>": {"get": {"operationId": "core_api_get_home_products", "summary": "Get Home Products", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "region_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Region Id"}, "required": false}, {"in": "query", "name": "wholesaler_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Wholesaler Id"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}, {"in": "query", "name": "category_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id"}, "required": false}, {"in": "query", "name": "company_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company Id"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedProductResponse"}}}}}, "description": "Get products for home page with pagination and filtering support.\n\nQuery Parameters:\n- page: Page number (default: 1)\n- page_size: Number of items per page (default: 20, max: 100)\n- region_id: Filter by region ID (includes parent regions)\n- wholesaler_id: Filter by specific wholesaler ID\n- search: Search term for product name/title/description\n- category_id: Filter by category ID\n- company_id: Filter by company ID", "tags": ["home"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/users/": {"get": {"operationId": "accounts_api_custom_user_list_users", "summary": "List Users", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}, {"in": "query", "name": "is_active", "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}, "required": false}, {"in": "query", "name": "phone_verified", "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Phone Verified"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedCustomUserResponse"}}}}}, "description": "List all users with pagination and filtering.\nRequires authentication.", "tags": ["users"], "security": [{"AuthBearerMiddleware": []}]}, "post": {"operationId": "accounts_api_custom_user_create_user", "summary": "Create User", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomUserOut"}}}}}, "description": "Create a new user.\nRequires authentication.", "tags": ["users"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomUserIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/users/{user_id}": {"get": {"operationId": "accounts_api_custom_user_get_user", "summary": "Get User", "parameters": [{"in": "path", "name": "user_id", "schema": {"title": "User Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomUserOut"}}}}}, "description": "Get a specific user by ID.\nRequires authentication.", "tags": ["users"], "security": [{"AuthBearerMiddleware": []}]}, "put": {"operationId": "accounts_api_custom_user_update_user", "summary": "Update User", "parameters": [{"in": "path", "name": "user_id", "schema": {"title": "User Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomUserOut"}}}}}, "description": "Update a user.\nRequires authentication.", "tags": ["users"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomUserUpdate"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}, "delete": {"operationId": "accounts_api_custom_user_delete_user", "summary": "Delete User", "parameters": [{"in": "path", "name": "user_id", "schema": {"title": "User Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Soft delete a user.\nRequires authentication.", "tags": ["users"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/notifications/": {"get": {"operationId": "accounts_api_notification_list_notifications", "summary": "List Notifications", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "user_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "User Id"}, "required": false}, {"in": "query", "name": "is_read", "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON>"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedNotificationResponse"}}}}}, "description": "List all notifications with pagination and filtering.\nRequires authentication.", "tags": ["notifications"], "security": [{"AuthBearerMiddleware": []}]}, "post": {"operationId": "accounts_api_notification_create_notification", "summary": "Create Notification", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationOut"}}}}}, "description": "Create a new notification.\nRequires authentication.", "tags": ["notifications"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/notifications/my": {"get": {"operationId": "accounts_api_notification_list_my_notifications", "summary": "List My Notifications", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "is_read", "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON>"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedNotificationResponse"}}}}}, "description": "List current user's notifications with pagination and filtering.\nRequires authentication.", "tags": ["notifications"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/notifications/{notification_id}": {"get": {"operationId": "accounts_api_notification_get_notification", "summary": "Get Notification", "parameters": [{"in": "path", "name": "notification_id", "schema": {"title": "Notification Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationOut"}}}}}, "description": "Get a specific notification by ID.\nRequires authentication.", "tags": ["notifications"], "security": [{"AuthBearerMiddleware": []}]}, "put": {"operationId": "accounts_api_notification_update_notification", "summary": "Update Notification", "parameters": [{"in": "path", "name": "notification_id", "schema": {"title": "Notification Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationOut"}}}}}, "description": "Update a notification.\nRequires authentication.", "tags": ["notifications"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationUpdate"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}, "delete": {"operationId": "accounts_api_notification_delete_notification", "summary": "Delete Notification", "parameters": [{"in": "path", "name": "notification_id", "schema": {"title": "Notification Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Soft delete a notification.\nRequires authentication.", "tags": ["notifications"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/notifications/mark-read": {"post": {"operationId": "accounts_api_notification_mark_notifications_read", "summary": "Mark Notifications Read", "parameters": [], "responses": {"200": {"description": "OK"}}, "description": "Mark multiple notifications as read.\nRequires authentication.", "tags": ["notifications"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationMarkReadRequest"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/products/": {"get": {"operationId": "products_api_product_list_products", "summary": "List Products", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}, {"in": "query", "name": "company_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company Id"}, "required": false}, {"in": "query", "name": "category_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id"}, "required": false}, {"in": "query", "name": "unit", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Unit"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedProductResponse"}}}}}, "description": "List all products with pagination and filtering.\nRequires authentication.", "tags": ["products"], "security": [{"AuthBearerMiddleware": []}]}, "post": {"operationId": "products_api_product_create_product", "summary": "Create Product", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductOut"}}}}}, "description": "Create a new product.\nRequires authentication.", "tags": ["products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/products/{product_id}": {"get": {"operationId": "products_api_product_get_product", "summary": "Get Product", "parameters": [{"in": "path", "name": "product_id", "schema": {"title": "Product Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductOut"}}}}}, "description": "Get a specific product by ID.\nRequires authentication.", "tags": ["products"], "security": [{"AuthBearerMiddleware": []}]}, "put": {"operationId": "products_api_product_update_product", "summary": "Update Product", "parameters": [{"in": "path", "name": "product_id", "schema": {"title": "Product Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductOut"}}}}}, "description": "Update a product.\nRequires authentication.", "tags": ["products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductUpdate"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}, "delete": {"operationId": "products_api_product_delete_product", "summary": "Delete Product", "parameters": [{"in": "path", "name": "product_id", "schema": {"title": "Product Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Soft delete a product.\nRequires authentication.", "tags": ["products"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/products/{product_id}/wholesaler-items": {"post": {"operationId": "products_api_product_get_product_with_wholesaler_items", "summary": "Get Product With Wholesaler Items", "parameters": [{"in": "path", "name": "product_id", "schema": {"title": "Product Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductWithWholesalerItemsOut"}}}}}, "description": "Get a product by its ID and return all wholesaler items associated with that product.\nFilters by region hierarchy to show only relevant wholesalers and their regional pricing.\n\nArgs:\n    product_id: The ID of the product to retrieve\n    data: Request body containing region_id for filtering\n\nReturns:\n    ProductWithWholesalerItemsOut: Product details with all associated wholesaler items", "tags": ["products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegionFilterIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/categories/": {"get": {"operationId": "products_api_category_list_categories", "summary": "List Categories", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedCategoryResponse"}}}}}, "description": "List all categories with pagination and filtering.\nRequires authentication.", "tags": ["categories"], "security": [{"AuthBearerMiddleware": []}]}, "post": {"operationId": "products_api_category_create_category", "summary": "Create Category", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryOut"}}}}}, "description": "Create a new category.\nRequires authentication.", "tags": ["categories"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/categories/{category_id}": {"get": {"operationId": "products_api_category_get_category", "summary": "Get Category", "parameters": [{"in": "path", "name": "category_id", "schema": {"title": "Category Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryOut"}}}}}, "description": "Get a specific category by ID.\nRequires authentication.", "tags": ["categories"], "security": [{"AuthBearerMiddleware": []}]}, "put": {"operationId": "products_api_category_update_category", "summary": "Update Category", "parameters": [{"in": "path", "name": "category_id", "schema": {"title": "Category Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryOut"}}}}}, "description": "Update a category.\nRequires authentication.", "tags": ["categories"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryUpdate"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}, "delete": {"operationId": "products_api_category_delete_category", "summary": "Delete Category", "parameters": [{"in": "path", "name": "category_id", "schema": {"title": "Category Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Soft delete a category.\nRequires authentication.", "tags": ["categories"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/categories/{category_id}/products": {"get": {"operationId": "products_api_category_get_category_products", "summary": "Get Category Products", "parameters": [{"in": "path", "name": "category_id", "schema": {"title": "Category Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Get all products in a category.\nRequires authentication.", "tags": ["categories"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/companies/": {"get": {"operationId": "products_api_company_list_companies", "summary": "List Companies", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedCompanyResponse"}}}}}, "description": "List all companies with pagination and filtering.\nRequires authentication.", "tags": ["companies"], "security": [{"AuthBearerMiddleware": []}]}, "post": {"operationId": "products_api_company_create_company", "summary": "Create Company", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyOut"}}}}}, "description": "Create a new company.\nRequires authentication.", "tags": ["companies"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/companies/{company_id}": {"get": {"operationId": "products_api_company_get_company", "summary": "Get Company", "parameters": [{"in": "path", "name": "company_id", "schema": {"title": "Company Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyOut"}}}}}, "description": "Get a specific company by ID.\nRequires authentication.", "tags": ["companies"], "security": [{"AuthBearerMiddleware": []}]}, "put": {"operationId": "products_api_company_update_company", "summary": "Update Company", "parameters": [{"in": "path", "name": "company_id", "schema": {"title": "Company Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyOut"}}}}}, "description": "Update a company.\nRequires authentication.", "tags": ["companies"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyUpdate"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}, "delete": {"operationId": "products_api_company_delete_company", "summary": "Delete Company", "parameters": [{"in": "path", "name": "company_id", "schema": {"title": "Company Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Soft delete a company.\nRequires authentication.", "tags": ["companies"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/companies/{company_id}/products": {"get": {"operationId": "products_api_company_get_company_products", "summary": "Get Company Products", "parameters": [{"in": "path", "name": "company_id", "schema": {"title": "Company Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Get all products from a company.\nRequires authentication.", "tags": ["companies"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/regions/": {"get": {"operationId": "products_api_region_list_regions", "summary": "List Regions", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}, {"in": "query", "name": "type", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type"}, "required": false}, {"in": "query", "name": "parent_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Parent Id"}, "required": false}, {"in": "query", "name": "is_active", "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedRegionResponse"}}}}}, "description": "List all regions with pagination and filtering.\nRequires authentication.", "tags": ["regions"], "security": [{"AuthBearerMiddleware": []}]}, "post": {"operationId": "products_api_region_create_region", "summary": "Create Region", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegionOut"}}}}}, "description": "Create a new region.\nRequires authentication.", "tags": ["regions"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegionIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/regions/hierarchy": {"get": {"operationId": "products_api_region_list_regions_with_hierarchy", "summary": "List Regions With Hierarchy", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RegionWithHierarchyOut"}, "title": "Response", "type": "array"}}}}}, "description": "List all regions with hierarchical names.\nRequires authentication.", "tags": ["regions"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/regions/countries": {"get": {"operationId": "products_api_region_get_countries", "summary": "Get Countries", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RegionWithHierarchyOut"}, "title": "Response", "type": "array"}}}}}, "description": "List all countries.\nRequires authentication.", "tags": ["regions"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/regions/states": {"get": {"operationId": "products_api_region_get_states", "summary": "Get States", "parameters": [{"in": "query", "name": "country_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Country Id"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RegionWithHierarchyOut"}, "title": "Response", "type": "array"}}}}}, "description": "List all states/provinces, optionally filtered by country.\nRequires authentication.", "tags": ["regions"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/regions/cities": {"get": {"operationId": "products_api_region_get_cities", "summary": "Get Cities", "parameters": [{"in": "query", "name": "state_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "State Id"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RegionWithHierarchyOut"}, "title": "Response", "type": "array"}}}}}, "description": "List all cities, optionally filtered by state.\nRequires authentication.", "tags": ["regions"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/regions/{region_id}": {"get": {"operationId": "products_api_region_get_region", "summary": "Get Region", "parameters": [{"in": "path", "name": "region_id", "schema": {"title": "Region Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegionOut"}}}}}, "description": "Get a specific region by ID.\nRequires authentication.", "tags": ["regions"], "security": [{"AuthBearerMiddleware": []}]}, "put": {"operationId": "products_api_region_update_region", "summary": "Update Region", "parameters": [{"in": "path", "name": "region_id", "schema": {"title": "Region Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegionOut"}}}}}, "description": "Update a region.\nRequires authentication.", "tags": ["regions"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegionUpdate"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}, "delete": {"operationId": "products_api_region_delete_region", "summary": "Delete Region", "parameters": [{"in": "path", "name": "region_id", "schema": {"title": "Region Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Soft delete a region.\nRequires authentication.", "tags": ["regions"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/regions/{region_id}/children": {"get": {"operationId": "products_api_region_get_region_children", "summary": "Get Region Children", "parameters": [{"in": "path", "name": "region_id", "schema": {"title": "Region Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RegionOut"}, "title": "Response", "type": "array"}}}}}, "description": "Get all child regions of a specific region.\nRequires authentication.", "tags": ["regions"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/stores/": {"get": {"operationId": "stores_api_store_list_stores", "summary": "List Stores", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}, {"in": "query", "name": "owner_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Owner Id"}, "required": false}, {"in": "query", "name": "city_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "City Id"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedStoreResponse"}}}}}, "description": "List all stores with pagination and filtering.\nRequires authentication.", "tags": ["stores"], "security": [{"AuthBearerMiddleware": []}]}, "post": {"operationId": "stores_api_store_create_store", "summary": "Create Store", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreOut"}}}}}, "description": "Create a new store.\nRequires authentication.", "tags": ["stores"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/stores/my": {"get": {"operationId": "stores_api_store_list_my_stores", "summary": "List My Stores", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedStoreResponse"}}}}}, "description": "List current user's stores with pagination.\nRequires authentication.", "tags": ["stores"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/stores/{store_id}": {"get": {"operationId": "stores_api_store_get_store", "summary": "Get Store", "parameters": [{"in": "path", "name": "store_id", "schema": {"title": "Store Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreOut"}}}}}, "description": "Get a specific store by ID.\nRequires authentication.", "tags": ["stores"], "security": [{"AuthBearerMiddleware": []}]}, "put": {"operationId": "stores_api_store_update_store", "summary": "Update Store", "parameters": [{"in": "path", "name": "store_id", "schema": {"title": "Store Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreOut"}}}}}, "description": "Update a store.\nRequires authentication.", "tags": ["stores"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoreUpdate"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}, "delete": {"operationId": "stores_api_store_delete_store", "summary": "Delete Store", "parameters": [{"in": "path", "name": "store_id", "schema": {"title": "Store Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Soft delete a store.\nRequires authentication.", "tags": ["stores"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/orders/": {"get": {"operationId": "stores_api_order_list_orders", "summary": "List Orders", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "status", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}, "required": false}, {"in": "query", "name": "store_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Store Id"}, "required": false}, {"in": "query", "name": "wholesaler_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Wholesaler Id"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedOrderResponse"}}}}}, "description": "List all orders with pagination and filtering.\nRequires authentication.", "tags": ["orders"], "security": [{"AuthBearerMiddleware": []}]}, "post": {"operationId": "stores_api_order_create_order", "summary": "Create Order", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOut"}}}}}, "description": "Create a new order.\nRequires authentication.", "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderIn"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}}, "/api/orders/{order_id}": {"get": {"operationId": "stores_api_order_get_order", "summary": "Get Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOut"}}}}}, "description": "Get a specific order by ID.\nRequires authentication.", "tags": ["orders"], "security": [{"AuthBearerMiddleware": []}]}, "put": {"operationId": "stores_api_order_update_order", "summary": "Update Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOut"}}}}}, "description": "Update an order.\nRequires authentication.", "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderUpdate"}}}, "required": true}, "security": [{"AuthBearerMiddleware": []}]}, "delete": {"operationId": "stores_api_order_delete_order", "summary": "Delete Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Soft delete an order.\nRequires authentication.", "tags": ["orders"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/wholesalers/": {"get": {"operationId": "wholesalers_api_wholesaler_list_wholesalers", "summary": "List Wholesalers", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}, {"in": "query", "name": "user_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "User Id"}, "required": false}, {"in": "query", "name": "region_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Region Id"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedWholesalerResponse"}}}}}, "description": "List all wholesalers with pagination and filtering.\nRequires authentication.", "tags": ["wholesalers"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/wholesalers/{wholesaler_id}": {"get": {"operationId": "wholesalers_api_wholesaler_get_wholesaler", "summary": "Get Wholesaler", "parameters": [{"in": "path", "name": "wholesaler_id", "schema": {"title": "Wholesaler Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WholesalerOut"}}}}}, "description": "Get a specific wholesaler by ID.\nRequires authentication.", "tags": ["wholesalers"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/min-charges/": {"get": {"operationId": "wholesalers_api_min_charge_list_min_charges", "summary": "List Min Charges", "parameters": [{"in": "query", "name": "region_id", "schema": {"title": "Region Id", "type": "integer"}, "required": true}, {"in": "query", "name": "wholesaler_id", "schema": {"title": "Wholesaler Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RegionMinChargeOut"}, "title": "Response", "type": "array"}}}}}, "description": "List all min charges.\nRequires authentication.", "tags": ["wholesalers"], "security": [{"AuthBearerMiddleware": []}]}}, "/api/items/wholesaler/{wholesaler_id}": {"get": {"operationId": "wholesalers_api_items_list_wholesaler_items", "summary": "List Wholesaler Items", "parameters": [{"in": "path", "name": "wholesaler_id", "schema": {"title": "Wholesaler Id", "type": "integer"}, "required": true}, {"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}, "required": false}, {"in": "query", "name": "category_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedItemsResponse"}}}}}, "description": "List items for a specific wholesaler including product and category data.\nExcludes items with zero inventory or zero price.", "tags": ["items"], "security": [{"AuthBearerMiddleware": []}]}}}, "components": {"schemas": {"LoginResponse": {"properties": {"success": {"title": "Success", "type": "boolean"}, "token": {"title": "Token", "type": "string"}, "user_id": {"title": "User Id", "type": "integer"}, "phone": {"title": "Phone", "type": "string"}, "is_phone_verified": {"title": "Is Phone Verified", "type": "boolean"}, "wholesaler_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Wholesaler Id"}}, "required": ["success", "token", "user_id", "phone", "is_phone_verified"], "title": "LoginResponse", "type": "object"}, "LoginRequest": {"properties": {"phone": {"title": "Phone", "type": "string"}, "password": {"title": "Password", "type": "string"}}, "required": ["phone", "password"], "title": "LoginRequest", "type": "object"}, "RegisterResponse": {"properties": {"success": {"title": "Success", "type": "boolean"}, "user_id": {"title": "User Id", "type": "integer"}, "phone": {"title": "Phone", "type": "string"}, "message": {"title": "Message", "type": "string"}, "token": {"title": "Token", "type": "string"}}, "required": ["success", "user_id", "phone", "message", "token"], "title": "RegisterResponse", "type": "object"}, "RegisterRequest": {"properties": {"name": {"title": "Name", "type": "string"}, "password": {"title": "Password", "type": "string"}, "phone": {"title": "Phone", "type": "string"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}}, "required": ["name", "password", "phone"], "title": "RegisterRequest", "type": "object"}, "UserResponse": {"description": "Response schema for user data", "properties": {"id": {"title": "Id", "type": "integer"}, "username": {"title": "Username", "type": "string"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "phone": {"title": "Phone", "type": "string"}, "phone_verified": {"title": "Phone Verified", "type": "boolean"}, "first_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "First Name"}, "last_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Name"}, "is_active": {"title": "Is Active", "type": "boolean"}, "date_joined": {"format": "date-time", "title": "Date Joined", "type": "string"}, "wholesaler_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Wholesaler Id"}}, "required": ["id", "username", "phone", "phone_verified", "is_active", "date_joined"], "title": "UserResponse", "type": "object"}, "UserUpdateRequest": {"description": "Request schema for updating user data", "properties": {"first_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "First Name"}, "last_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Name"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}}, "title": "UserUpdateRequest", "type": "object"}, "CategoryOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "slug": {"title": "Slug", "type": "string"}}, "required": ["id", "name", "title", "slug"], "title": "CategoryOut", "type": "object"}, "CompanyOut": {"description": "Schema for company output", "properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "slug": {"title": "Slug", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "name", "title", "slug", "created_at", "updated_at"], "title": "CompanyOut", "type": "object"}, "PaginatedProductResponse": {"description": "Paginated response for products", "properties": {"products": {"items": {"$ref": "#/components/schemas/ProductOut"}, "title": "Products", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["products", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedProductResponse", "type": "object"}, "ProductWithPricing": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "barcode": {"title": "Barcode", "type": "string"}, "slug": {"title": "Slug", "type": "string"}, "description": {"title": "Description", "type": "string"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url"}, "company_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company Id"}, "category_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id"}, "company": {"anyOf": [{"$ref": "#/components/schemas/CompanyOut"}, {"type": "null"}]}, "category": {"anyOf": [{"$ref": "#/components/schemas/CategoryOut"}, {"type": "null"}]}, "unit": {"title": "Unit", "type": "string"}, "unit_count": {"title": "Unit Count", "type": "number"}, "base_price": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Base Price"}, "other_price": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Other Price"}}, "required": ["id", "name", "title", "barcode", "slug", "description", "unit", "unit_count"], "title": "ProductWithPricing", "type": "object"}, "CustomUserOut": {"description": "Simplified user schema for wholesaler responses", "properties": {"id": {"title": "Id", "type": "integer"}, "username": {"title": "Username", "type": "string"}, "phone": {"title": "Phone", "type": "string"}}, "required": ["id", "username", "phone"], "title": "CustomUserOut", "type": "object"}, "PaginatedCustomUserResponse": {"description": "Paginated response for users", "properties": {"users": {"items": {"$ref": "#/components/schemas/CustomUserOut"}, "title": "Users", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["users", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedCustomUserResponse", "type": "object"}, "CustomUserIn": {"description": "Schema for creating a new user", "properties": {"username": {"title": "Username", "type": "string"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "phone": {"title": "Phone", "type": "string"}, "password": {"title": "Password", "type": "string"}, "first_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "First Name"}, "last_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Name"}}, "required": ["username", "phone", "password"], "title": "CustomUserIn", "type": "object"}, "CustomUserUpdate": {"description": "Schema for updating user data", "properties": {"username": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Username"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "first_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "First Name"}, "last_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Last Name"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}}, "title": "CustomUserUpdate", "type": "object"}, "NotificationOut": {"description": "Schema for notification output", "properties": {"id": {"title": "Id", "type": "integer"}, "user": {"$ref": "#/components/schemas/CustomUserOut"}, "title": {"title": "Title", "type": "string"}, "body": {"title": "Body", "type": "string"}, "is_read": {"title": "<PERSON>", "type": "boolean"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "user", "title", "body", "is_read", "created_at", "updated_at"], "title": "NotificationOut", "type": "object"}, "PaginatedNotificationResponse": {"description": "Paginated response for notifications", "properties": {"notifications": {"items": {"$ref": "#/components/schemas/NotificationOut"}, "title": "Notifications", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["notifications", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedNotificationResponse", "type": "object"}, "NotificationIn": {"description": "Schema for creating a new notification", "properties": {"user_id": {"title": "User Id", "type": "integer"}, "title": {"title": "Title", "type": "string"}, "body": {"title": "Body", "type": "string"}}, "required": ["user_id", "title", "body"], "title": "NotificationIn", "type": "object"}, "NotificationUpdate": {"description": "Schema for updating notification data", "properties": {"title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title"}, "body": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Body"}, "is_read": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "<PERSON>"}}, "title": "NotificationUpdate", "type": "object"}, "NotificationMarkReadRequest": {"description": "Schema for marking notifications as read", "properties": {"notification_ids": {"items": {"type": "integer"}, "title": "Notification Ids", "type": "array"}}, "required": ["notification_ids"], "title": "NotificationMarkReadRequest", "type": "object"}, "ProductOut": {"description": "Schema for product output", "properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "barcode": {"title": "Barcode", "type": "string"}, "slug": {"title": "Slug", "type": "string"}, "description": {"title": "Description", "type": "string"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url"}, "company": {"anyOf": [{"$ref": "#/components/schemas/CompanyOut"}, {"type": "null"}]}, "category": {"anyOf": [{"$ref": "#/components/schemas/CategoryOut"}, {"type": "null"}]}, "unit": {"title": "Unit", "type": "string"}, "unit_count": {"title": "Unit Count", "type": "number"}, "items_count": {"title": "Items Count", "type": "integer"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "name", "title", "barcode", "slug", "description", "unit", "unit_count", "items_count", "created_at", "updated_at"], "title": "ProductOut", "type": "object"}, "ProductIn": {"description": "Schema for creating a new product", "properties": {"name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "barcode": {"title": "Barcode", "type": "string"}, "slug": {"title": "Slug", "type": "string"}, "description": {"default": "", "title": "Description", "type": "string"}, "company_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company Id"}, "category_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id"}, "unit": {"default": "PIECE", "title": "Unit", "type": "string"}, "unit_count": {"anyOf": [{"type": "number"}, {"type": "string"}], "default": "1.0", "title": "Unit Count"}}, "required": ["name", "title", "barcode", "slug"], "title": "ProductIn", "type": "object"}, "ProductUpdate": {"description": "Schema for updating product data", "properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title"}, "barcode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Barcode"}, "slug": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Slug"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "company_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company Id"}, "category_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Category Id"}, "unit": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Unit"}, "unit_count": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Unit Count"}}, "title": "ProductUpdate", "type": "object"}, "ProductWithWholesalerItemsOut": {"description": "Schema for product with all associated wholesaler items", "properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "barcode": {"title": "Barcode", "type": "string"}, "slug": {"title": "Slug", "type": "string"}, "description": {"title": "Description", "type": "string"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url"}, "company": {"anyOf": [{"$ref": "#/components/schemas/CompanyOut"}, {"type": "null"}]}, "category": {"anyOf": [{"$ref": "#/components/schemas/CategoryOut"}, {"type": "null"}]}, "unit": {"title": "Unit", "type": "string"}, "unit_count": {"title": "Unit Count", "type": "number"}, "items_count": {"title": "Items Count", "type": "integer"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}, "wholesaler_items": {"items": {"$ref": "#/components/schemas/WholesalerItemWithDetailsOut"}, "title": "Wholesaler Items", "type": "array"}}, "required": ["id", "name", "title", "barcode", "slug", "description", "unit", "unit_count", "items_count", "created_at", "updated_at", "wholesaler_items"], "title": "ProductWithWholesalerItemsOut", "type": "object"}, "RegionMinChargeOut": {"description": "Schema for RegionMinCharge output", "properties": {"id": {"title": "Id", "type": "integer"}, "min_charge": {"title": "Min Charge", "type": "number"}, "min_items": {"title": "Min Items", "type": "integer"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "min_charge", "min_items", "created_at", "updated_at"], "title": "RegionMinChargeOut", "type": "object"}, "WholesalerDetailOut": {"description": "Schema for detailed wholesaler information", "properties": {"id": {"title": "Id", "type": "integer"}, "category": {"title": "Category", "type": "string"}, "title": {"title": "Title", "type": "string"}, "username": {"title": "Username", "type": "string"}, "logo_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Logo Url"}, "background_image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Background Image Url"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "category", "title", "username", "created_at", "updated_at"], "title": "WholesalerDetailOut", "type": "object"}, "WholesalerItemOut": {"description": "Schema for wholesaler item output", "properties": {"id": {"title": "Id", "type": "integer"}, "base_price": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Base Price"}, "inventory_count": {"title": "Inventory Count", "type": "integer"}, "minimum_order_quantity": {"title": "Minimum Order Quantity", "type": "integer"}, "maximum_order_quantity": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Maximum Order Quantity"}, "price_expiry": {"format": "date-time", "title": "Price Expiry", "type": "string"}, "expires_at": {"format": "date-time", "title": "Expires At", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "base_price", "inventory_count", "minimum_order_quantity", "price_expiry", "expires_at", "created_at", "updated_at"], "title": "WholesalerItemOut", "type": "object"}, "WholesalerItemWithDetailsOut": {"description": "Schema combining wholesaler item with wholesaler details and regional pricing", "properties": {"item": {"$ref": "#/components/schemas/WholesalerItemOut"}, "wholesaler": {"$ref": "#/components/schemas/WholesalerDetailOut"}, "regional_pricing": {"anyOf": [{"$ref": "#/components/schemas/RegionMinChargeOut"}, {"type": "null"}]}}, "required": ["item", "wholesaler"], "title": "WholesalerItemWithDetailsOut", "type": "object"}, "RegionFilterIn": {"description": "Schema for region filter input", "properties": {"region_id": {"title": "Region Id", "type": "integer"}}, "required": ["region_id"], "title": "RegionFilterIn", "type": "object"}, "PaginatedCategoryResponse": {"description": "Paginated response for categories", "properties": {"categories": {"items": {"$ref": "#/components/schemas/CategoryOut"}, "title": "Categories", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["categories", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedCategoryResponse", "type": "object"}, "CategoryIn": {"description": "Schema for creating a new category", "properties": {"name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "slug": {"title": "Slug", "type": "string"}}, "required": ["name", "title", "slug"], "title": "CategoryIn", "type": "object"}, "CategoryUpdate": {"description": "Schema for updating category data", "properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title"}, "slug": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Slug"}}, "title": "CategoryUpdate", "type": "object"}, "PaginatedCompanyResponse": {"description": "Paginated response for companies", "properties": {"companies": {"items": {"$ref": "#/components/schemas/CompanyOut"}, "title": "Companies", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["companies", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedCompanyResponse", "type": "object"}, "CompanyIn": {"description": "Schema for creating a new company", "properties": {"name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "slug": {"title": "Slug", "type": "string"}}, "required": ["name", "title", "slug"], "title": "CompanyIn", "type": "object"}, "CompanyUpdate": {"description": "Schema for updating company data", "properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title"}, "slug": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Slug"}}, "title": "CompanyUpdate", "type": "object"}, "PaginatedRegionResponse": {"description": "Paginated response for regions", "properties": {"regions": {"items": {"$ref": "#/components/schemas/RegionOut"}, "title": "Regions", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["regions", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedRegionResponse", "type": "object"}, "RegionOut": {"description": "Simplified region schema for store responses", "properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "type": {"title": "Type", "type": "string"}, "full_path": {"title": "Full Path", "type": "string"}}, "required": ["id", "name", "type", "full_path"], "title": "RegionOut", "type": "object"}, "RegionIn": {"description": "Schema for creating a new region", "properties": {"name": {"title": "Name", "type": "string"}, "type": {"title": "Type", "type": "string"}, "parent_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Parent Id"}, "code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code"}, "slug": {"title": "Slug", "type": "string"}, "is_active": {"default": true, "title": "Is Active", "type": "boolean"}}, "required": ["name", "type", "slug"], "title": "RegionIn", "type": "object"}, "RegionWithHierarchyOut": {"description": "Schema for region output with hierarchy information", "properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "type": {"title": "Type", "type": "string"}, "parent_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Parent Id"}, "code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code"}, "slug": {"title": "Slug", "type": "string"}, "is_active": {"title": "Is Active", "type": "boolean"}, "full_path": {"title": "Full Path", "type": "string"}, "hierarchical_name": {"title": "Hierarchical Name", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "name", "type", "slug", "is_active", "full_path", "hierarchical_name", "created_at", "updated_at"], "title": "RegionWithHierarchyOut", "type": "object"}, "RegionUpdate": {"description": "Schema for updating region data", "properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type"}, "parent_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Parent Id"}, "code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code"}, "slug": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Slug"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}}, "title": "RegionUpdate", "type": "object"}, "PaginatedStoreResponse": {"description": "Paginated response for stores", "properties": {"stores": {"items": {"$ref": "#/components/schemas/StoreOut"}, "title": "Stores", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["stores", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedStoreResponse", "type": "object"}, "StoreOut": {"description": "Schema for store output", "properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "description": {"title": "Description", "type": "string"}, "address": {"title": "Address", "type": "string"}, "owner": {"$ref": "#/components/schemas/CustomUserOut"}, "city": {"anyOf": [{"$ref": "#/components/schemas/RegionOut"}, {"type": "null"}]}, "state": {"anyOf": [{"$ref": "#/components/schemas/RegionOut"}, {"type": "null"}]}, "country": {"anyOf": [{"$ref": "#/components/schemas/RegionOut"}, {"type": "null"}]}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "name", "description", "address", "owner", "created_at", "updated_at"], "title": "StoreOut", "type": "object"}, "StoreIn": {"description": "Schema for creating a new store", "properties": {"name": {"title": "Name", "type": "string"}, "description": {"default": "", "title": "Description", "type": "string"}, "address": {"title": "Address", "type": "string"}, "city_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "City Id"}, "state_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "State Id"}, "country_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Country Id"}, "owner_id": {"title": "Owner Id", "type": "integer"}}, "required": ["name", "address", "owner_id"], "title": "StoreIn", "type": "object"}, "StoreUpdate": {"description": "Schema for updating store data", "properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address"}, "city_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "City Id"}, "state_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "State Id"}, "country_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Country Id"}}, "title": "StoreUpdate", "type": "object"}, "OrderOut": {"description": "Schema for order output", "properties": {"id": {"title": "Id", "type": "integer"}, "wholesaler": {"$ref": "#/components/schemas/WholesalerOut"}, "store": {"$ref": "#/components/schemas/StoreOrderOut"}, "total_price": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Total Price"}, "fees": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Fees"}, "deliver_at": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Deliver At"}, "products_total_price": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Products Total Price"}, "products_total_quantity": {"title": "Products Total Quantity", "type": "integer"}, "final_completed_price": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Final Completed Price"}, "completed_at": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Completed At"}, "status": {"title": "Status", "type": "string"}, "status_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status Reason"}, "status_updated_at": {"format": "date-time", "title": "Status Updated At", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "wholesaler", "store", "total_price", "fees", "products_total_price", "products_total_quantity", "status", "status_updated_at", "created_at", "updated_at"], "title": "OrderOut", "type": "object"}, "PaginatedOrderResponse": {"description": "Paginated response for orders", "properties": {"orders": {"items": {"$ref": "#/components/schemas/OrderOut"}, "title": "Orders", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["orders", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedOrderResponse", "type": "object"}, "StoreOrderOut": {"description": "Simplified store schema for order responses", "properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "address": {"title": "Address", "type": "string"}}, "required": ["id", "name", "address"], "title": "StoreOrderOut", "type": "object"}, "WholesalerOut": {"description": "Schema for wholesaler output", "properties": {"id": {"title": "Id", "type": "integer"}, "title": {"title": "Title", "type": "string"}, "description": {"title": "Description", "type": "string"}, "phone": {"title": "Phone", "type": "string"}, "address": {"title": "Address", "type": "string"}, "logo_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Logo Url"}, "background_image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Background Image Url"}, "user": {"$ref": "#/components/schemas/CustomUserOut"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "title", "description", "phone", "address", "user", "created_at", "updated_at"], "title": "WholesalerOut", "type": "object"}, "OrderIn": {"description": "Schema for creating a new order", "properties": {"wholesaler_id": {"title": "Wholesaler Id", "type": "integer"}, "store_id": {"title": "Store Id", "type": "integer"}, "total_price": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Total Price"}, "fees": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Fees"}, "products_total_price": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Products Total Price"}, "products_total_quantity": {"title": "Products Total Quantity", "type": "integer"}, "deliver_at": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Deliver At"}}, "required": ["wholesaler_id", "store_id", "total_price", "fees", "products_total_price", "products_total_quantity"], "title": "OrderIn", "type": "object"}, "OrderUpdate": {"description": "Schema for updating order data", "properties": {"status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}, "status_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status Reason"}, "deliver_at": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Deliver At"}, "final_completed_price": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Final Completed Price"}}, "title": "OrderUpdate", "type": "object"}, "PaginatedWholesalerResponse": {"description": "Paginated response for wholesalers", "properties": {"wholesalers": {"items": {"$ref": "#/components/schemas/WholesalerOut"}, "title": "Wholesalers", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["wholesalers", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedWholesalerResponse", "type": "object"}, "ItemOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "wholesaler_id": {"title": "Wholesaler Id", "type": "integer"}, "product": {"$ref": "#/components/schemas/ItemProductOut"}, "base_price": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Base Price"}, "inventory_count": {"title": "Inventory Count", "type": "integer"}, "minimum_order_quantity": {"title": "Minimum Order Quantity", "type": "integer"}, "maximum_order_quantity": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Maximum Order Quantity"}}, "required": ["id", "wholesaler_id", "product", "base_price", "inventory_count", "minimum_order_quantity"], "title": "ItemOut", "type": "object"}, "ItemProductOut": {"properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "title": {"title": "Title", "type": "string"}, "barcode": {"title": "Barcode", "type": "string"}, "slug": {"title": "Slug", "type": "string"}, "description": {"title": "Description", "type": "string"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url"}, "category": {"anyOf": [{"$ref": "#/components/schemas/CategoryOut"}, {"type": "null"}]}, "unit": {"title": "Unit", "type": "string"}, "unit_count": {"title": "Unit Count", "type": "number"}}, "required": ["id", "name", "title", "barcode", "slug", "description", "unit", "unit_count"], "title": "ItemProductOut", "type": "object"}, "PaginatedItemsResponse": {"properties": {"items": {"items": {"$ref": "#/components/schemas/ItemOut"}, "title": "Items", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}, "has_next": {"title": "Has Next", "type": "boolean"}, "has_previous": {"title": "Has Previous", "type": "boolean"}}, "required": ["items", "total_count", "page", "page_size", "total_pages", "has_next", "has_previous"], "title": "PaginatedItemsResponse", "type": "object"}}, "securitySchemes": {"AuthBearerMiddleware": {"type": "http", "scheme": "bearer"}}}, "servers": []}