import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/core/api/models/product_out.dart';
import 'package:tagerplus/components/network_image.dart';
import 'package:tagerplus/pages/product_details/product_details.dart';

class ProductTileSquare extends StatelessWidget {
  const ProductTileSquare({super.key, required this.product, this.onTap});

  final ProductOut product;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDefaults.padding / 2),
      child: Material(
        borderRadius: AppDefaults.borderRadius,
        color: AppColors.scaffoldBackground,
        child: InkWell(
          borderRadius: AppDefaults.borderRadius,
          onTap:
              onTap ??
              () {
                // Navigate to product details page
                Get.to(
                  () => const ProductDetailsPage(),
                  arguments: {'product': product},
                );
              },
          child: Container(
            width: 176,
            height: 296,
            padding: const EdgeInsets.all(AppDefaults.padding),
            decoration: BoxDecoration(
              border: Border.all(width: 0.1, color: AppColors.placeholder),
              borderRadius: AppDefaults.borderRadius,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product Image
                Padding(
                  padding: const EdgeInsets.all(AppDefaults.padding / 2),
                  child: AspectRatio(
                    aspectRatio: 1 / 1,
                    child: NetworkImageWithLoader(
                      product.imageUrl ?? '',
                      fit: BoxFit.contain,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(height: 8),

                // Product Name
                Text(
                  product.title,
                  style: Theme.of(
                    context,
                  ).textTheme.titleMedium?.copyWith(color: Colors.black),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const Spacer(),

                // Product Description/Weight
                if (product.description.isNotEmpty)
                  Text(
                    product.description,
                    style: Theme.of(
                      context,
                    ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                const SizedBox(height: 4),

                // Price Section
                _buildPriceSection(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPriceSection(BuildContext context) {
    // Generate mock pricing based on product ID for demonstration
    final mockPrice = _generateMockPrice();
    final hasDiscount = mockPrice['hasDiscount'] as bool;
    final currentPrice = mockPrice['currentPrice'] as double;
    final originalPrice = mockPrice['originalPrice'] as double?;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Current Price
        Text(
          '${currentPrice.toStringAsFixed(2)} ${'currency'.tr}',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: AppColors.primary,
            fontWeight: FontWeight.bold,
          ),
        ),

        // Original Price (if discounted)
        if (hasDiscount && originalPrice != null) ...[
          const SizedBox(height: 2),
          Text(
            '${originalPrice.toStringAsFixed(2)} ${'currency'.tr}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey,
              decoration: TextDecoration.lineThrough,
            ),
          ),
        ],
      ],
    );
  }

  /// Generate mock pricing data based on product ID
  Map<String, dynamic> _generateMockPrice() {
    // Use product ID hash to generate consistent pricing
    final hash = product.id.hashCode.abs();

    // Base price between 15-200 EGP
    final basePrice = 15.0 + (hash % 185);

    // 30% chance of having a discount
    final hasDiscount = (hash % 10) < 3;

    if (hasDiscount) {
      // Discount between 10-30%
      final discountPercent = 0.1 + ((hash % 20) / 100);
      final discountedPrice = basePrice * (1 - discountPercent);

      return {
        'hasDiscount': true,
        'currentPrice': discountedPrice,
        'originalPrice': basePrice,
      };
    } else {
      return {
        'hasDiscount': false,
        'currentPrice': basePrice,
        'originalPrice': null,
      };
    }
  }
}
