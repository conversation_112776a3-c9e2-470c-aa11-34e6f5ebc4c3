import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';

import 'package:tagerplus/core/api/models/wholesaler_out.dart';
import 'package:tagerplus/components/network_image.dart';

class WholesalerCard extends StatelessWidget {
  const WholesalerCard({
    super.key,
    required this.wholesaler,
    required this.onTap,
    this.hasCart = false,
    this.cartItemCount = 0,
  });

  final WholesalerOut wholesaler;
  final VoidCallback onTap;
  final bool hasCart;
  final int cartItemCount;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: hasCart ? AppColors.primary : Colors.grey[200]!,
              width: hasCart ? 2.5 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: hasCart
                    ? AppColors.primary.withValues(alpha: 0.15)
                    : Colors.black.withValues(alpha: 0.08),
                blurRadius: hasCart ? 12 : 8,
                spreadRadius: 0,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Stack(
            children: [
              Column(
                children: [
                  // Logo section
                  Expanded(
                    flex: 3,
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.grey[25],
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(16),
                          topRight: Radius.circular(16),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(12),
                        child:
                            wholesaler.logoUrl != null &&
                                wholesaler.logoUrl!.isNotEmpty
                            ? NetworkImageWithLoader(
                                wholesaler.logoUrl!,
                                fit: BoxFit.contain,
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(16),
                                  topRight: Radius.circular(16),
                                ),
                              )
                            : Icon(
                                Icons.store_outlined,
                                size: 48,
                                color: Colors.grey[400],
                              ),
                      ),
                    ),
                  ),

                  // Title section
                  Expanded(
                    flex: 2,
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            wholesaler.title,
                            style: Theme.of(context).textTheme.titleSmall
                                ?.copyWith(
                                  fontWeight: FontWeight.w700,
                                  color: hasCart
                                      ? AppColors.primary
                                      : Colors.black87,
                                  height: 1.2,
                                ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (wholesaler.description.isNotEmpty) ...[
                            const SizedBox(height: 4),
                            Text(
                              wholesaler.description,
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                    color: Colors.grey[600],
                                    fontSize: 11,
                                    height: 1.3,
                                  ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                ],
              ),

              // Cart indicator
              if (hasCart)
                Positioned(
                  top: 10,
                  right: 10,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primary.withValues(alpha: 0.3),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Text(
                      cartItemCount.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
