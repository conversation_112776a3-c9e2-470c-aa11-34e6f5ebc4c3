// Generated API client for NinjaAPI
// This file is auto-generated. Do not edit manually.

import 'package:dio/dio.dart';

import 'core_service.dart';
import 'auth_service.dart';
import 'home_service.dart';
import 'users_service.dart';
import 'notifications_service.dart';
import 'products_service.dart';
import 'categories_service.dart';
import 'companies_service.dart';
import 'regions_service.dart';
import 'stores_service.dart';
import 'orders_service.dart';
import 'wholesalers_service.dart';
import 'items_service.dart';

/// API client for NinjaAPI
class ApiClient {
  final Dio _dio;

  late final CoreService core;
  late final AuthService auth;
  late final HomeService home;
  late final UsersService users;
  late final NotificationsService notifications;
  late final ProductsService products;
  late final CategoriesService categories;
  late final CompaniesService companies;
  late final RegionsService regions;
  late final StoresService stores;
  late final OrdersService orders;
  late final WholesalersService wholesalers;
  late final ItemsService items;

  ApiClient({
    String? baseUrl,
    Dio? dio,
    Map<String, String>? headers,
    Duration? connectTimeout,
    Duration? receiveTimeout,
  }) : _dio = dio ?? Dio() {
    // Configure base options
    _dio.options.baseUrl = baseUrl ?? '';
    _dio.options.connectTimeout = connectTimeout ?? const Duration(seconds: 30);
    _dio.options.receiveTimeout = receiveTimeout ?? const Duration(seconds: 30);

    // Set default headers
    _dio.options.headers['Content-Type'] = 'application/json';
    _dio.options.headers['Accept'] = 'application/json';

    // Add custom headers
    if (headers != null) {
      _dio.options.headers.addAll(headers);
    }

    core = CoreService(_dio);
    auth = AuthService(_dio);
    home = HomeService(_dio);
    users = UsersService(_dio);
    notifications = NotificationsService(_dio);
    products = ProductsService(_dio);
    categories = CategoriesService(_dio);
    companies = CompaniesService(_dio);
    regions = RegionsService(_dio);
    stores = StoresService(_dio);
    orders = OrdersService(_dio);
    wholesalers = WholesalersService(_dio);
    items = ItemsService(_dio);
  }

  /// Access to the underlying Dio instance
  Dio get dio => _dio;

  /// Add an interceptor to the Dio instance
  void addInterceptor(Interceptor interceptor) {
    _dio.interceptors.add(interceptor);
  }

  /// Set authorization header
  void setAuthorizationHeader(String token, {String scheme = "Bearer"}) {
    _dio.options.headers["Authorization"] = "$scheme $token";
  }

  /// Remove authorization header
  void removeAuthorizationHeader() {
    _dio.options.headers.remove("Authorization");
  }
}
