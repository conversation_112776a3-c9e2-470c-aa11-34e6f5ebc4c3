// Generated service class for categories operations
// This file is auto-generated. Do not edit manually.

import 'package:dio/dio.dart';
import '../models/models.dart';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';

/// Service class for categories operations
class CategoriesService {
  final Dio _dio;

  const CategoriesService(this._dio);

  /// List Categories
  ///
  /// List all categories with pagination and filtering.
  /// Requires authentication.
  ///
  Future<Response<PaginatedCategoryResponse>> listCategories({int? page, int? pageSize, String? search}) async {
    const path = '/api/categories/';
    final queryParameters = <String, dynamic>{};
    if (page != null) {
      queryParameters['page'] = page;
    }
    if (pageSize != null) {
      queryParameters['page_size'] = pageSize;
    }
    if (search != null) {
      queryParameters['search'] = search;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseData = PaginatedCategoryResponse.fromJson(response.data as Map<String, dynamic>);
      return Response<PaginatedCategoryResponse>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Create Category
  ///
  /// Create a new category.
  /// Requires authentication.
  ///
  Future<Response<CategoryOut>> createCategory(CategoryIn requestData) async {
    const path = '/api/categories/';
    try {
      final response = await _dio.post(path, data: requestData.toJson());
      final responseData = CategoryOut.fromJson(response.data as Map<String, dynamic>);
      return Response<CategoryOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Get Category
  ///
  /// Get a specific category by ID.
  /// Requires authentication.
  ///
  Future<Response<CategoryOut>> getCategory(int categoryId) async {
    String path = '/api/categories/{category_id}';
    path = path.replaceAll('{category_id}', categoryId.toString());
    try {
      final response = await _dio.get(path);
      final responseData = CategoryOut.fromJson(response.data as Map<String, dynamic>);
      return Response<CategoryOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Update Category
  ///
  /// Update a category.
  /// Requires authentication.
  ///
  Future<Response<CategoryOut>> updateCategory(int categoryId, CategoryUpdate requestData) async {
    String path = '/api/categories/{category_id}';
    path = path.replaceAll('{category_id}', categoryId.toString());
    try {
      final response = await _dio.put(path, data: requestData.toJson());
      final responseData = CategoryOut.fromJson(response.data as Map<String, dynamic>);
      return Response<CategoryOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Delete Category
  ///
  /// Soft delete a category.
  /// Requires authentication.
  ///
  Future<Response<dynamic>> deleteCategory(int categoryId) async {
    String path = '/api/categories/{category_id}';
    path = path.replaceAll('{category_id}', categoryId.toString());
    try {
      final response = await _dio.delete(path);
      return response;
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Get Category Products
  ///
  /// Get all products in a category.
  /// Requires authentication.
  ///
  Future<Response<dynamic>> getCategoryProducts(int categoryId) async {
    String path = '/api/categories/{category_id}/products';
    path = path.replaceAll('{category_id}', categoryId.toString());
    try {
      final response = await _dio.get(path);
      return response;
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

}
