// Generated service class for items operations
// This file is auto-generated. Do not edit manually.

import 'package:dio/dio.dart';
import '../models/models.dart';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';

/// Service class for items operations
class ItemsService {
  final Dio _dio;

  const ItemsService(this._dio);

  /// List Wholesaler Items
  ///
  /// List items for a specific wholesaler including product and category data.
  /// Excludes items with zero inventory or zero price.
  ///
  Future<Response<PaginatedItemsResponse>> listWholesalerItems(int wholesalerId, {int? page, int? pageSize, String? search, int? categoryId}) async {
    String path = '/api/items/wholesaler/{wholesaler_id}';
    path = path.replaceAll('{wholesaler_id}', wholesalerId.toString());
    final queryParameters = <String, dynamic>{};
    if (page != null) {
      queryParameters['page'] = page;
    }
    if (pageSize != null) {
      queryParameters['page_size'] = pageSize;
    }
    if (search != null) {
      queryParameters['search'] = search;
    }
    if (categoryId != null) {
      queryParameters['category_id'] = categoryId;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseData = PaginatedItemsResponse.fromJson(response.data as Map<String, dynamic>);
      return Response<PaginatedItemsResponse>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

}
