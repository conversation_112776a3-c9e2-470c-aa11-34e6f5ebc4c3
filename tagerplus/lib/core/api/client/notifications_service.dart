// Generated service class for notifications operations
// This file is auto-generated. Do not edit manually.

import 'package:dio/dio.dart';
import '../models/models.dart';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';

/// Service class for notifications operations
class NotificationsService {
  final Dio _dio;

  const NotificationsService(this._dio);

  /// List Notifications
  ///
  /// List all notifications with pagination and filtering.
  /// Requires authentication.
  ///
  Future<Response<PaginatedNotificationResponse>> listNotifications({int? page, int? pageSize, int? userId, bool? isRead, String? search}) async {
    const path = '/api/notifications/';
    final queryParameters = <String, dynamic>{};
    if (page != null) {
      queryParameters['page'] = page;
    }
    if (pageSize != null) {
      queryParameters['page_size'] = pageSize;
    }
    if (userId != null) {
      queryParameters['user_id'] = userId;
    }
    if (isRead != null) {
      queryParameters['is_read'] = isRead;
    }
    if (search != null) {
      queryParameters['search'] = search;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseData = PaginatedNotificationResponse.fromJson(response.data as Map<String, dynamic>);
      return Response<PaginatedNotificationResponse>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Create Notification
  ///
  /// Create a new notification.
  /// Requires authentication.
  ///
  Future<Response<NotificationOut>> createNotification(NotificationIn requestData) async {
    const path = '/api/notifications/';
    try {
      final response = await _dio.post(path, data: requestData.toJson());
      final responseData = NotificationOut.fromJson(response.data as Map<String, dynamic>);
      return Response<NotificationOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// List My Notifications
  ///
  /// List current user's notifications with pagination and filtering.
  /// Requires authentication.
  ///
  Future<Response<PaginatedNotificationResponse>> listMyNotifications({int? page, int? pageSize, bool? isRead}) async {
    const path = '/api/notifications/my';
    final queryParameters = <String, dynamic>{};
    if (page != null) {
      queryParameters['page'] = page;
    }
    if (pageSize != null) {
      queryParameters['page_size'] = pageSize;
    }
    if (isRead != null) {
      queryParameters['is_read'] = isRead;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseData = PaginatedNotificationResponse.fromJson(response.data as Map<String, dynamic>);
      return Response<PaginatedNotificationResponse>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Get Notification
  ///
  /// Get a specific notification by ID.
  /// Requires authentication.
  ///
  Future<Response<NotificationOut>> getNotification(int notificationId) async {
    String path = '/api/notifications/{notification_id}';
    path = path.replaceAll('{notification_id}', notificationId.toString());
    try {
      final response = await _dio.get(path);
      final responseData = NotificationOut.fromJson(response.data as Map<String, dynamic>);
      return Response<NotificationOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Update Notification
  ///
  /// Update a notification.
  /// Requires authentication.
  ///
  Future<Response<NotificationOut>> updateNotification(int notificationId, NotificationUpdate requestData) async {
    String path = '/api/notifications/{notification_id}';
    path = path.replaceAll('{notification_id}', notificationId.toString());
    try {
      final response = await _dio.put(path, data: requestData.toJson());
      final responseData = NotificationOut.fromJson(response.data as Map<String, dynamic>);
      return Response<NotificationOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Delete Notification
  ///
  /// Soft delete a notification.
  /// Requires authentication.
  ///
  Future<Response<dynamic>> deleteNotification(int notificationId) async {
    String path = '/api/notifications/{notification_id}';
    path = path.replaceAll('{notification_id}', notificationId.toString());
    try {
      final response = await _dio.delete(path);
      return response;
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Mark Notifications Read
  ///
  /// Mark multiple notifications as read.
  /// Requires authentication.
  ///
  Future<Response<dynamic>> markNotificationsRead(NotificationMarkReadRequest requestData) async {
    const path = '/api/notifications/mark-read';
    try {
      final response = await _dio.post(path, data: requestData.toJson());
      return response;
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

}
