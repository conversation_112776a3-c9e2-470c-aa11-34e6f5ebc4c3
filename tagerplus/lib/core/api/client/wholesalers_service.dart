// Generated service class for wholesalers operations
// This file is auto-generated. Do not edit manually.

import 'package:dio/dio.dart';
import '../models/models.dart';
import 'package:openapi_dart_gen/openapi_dart_gen.dart';

/// Service class for wholesalers operations
class WholesalersService {
  final Dio _dio;

  const WholesalersService(this._dio);

  /// List Wholesalers
  ///
  /// List all wholesalers with pagination and filtering.
  /// Requires authentication.
  ///
  Future<Response<PaginatedWholesalerResponse>> listWholesalers({int? page, int? pageSize, String? search, int? userId, int? regionId}) async {
    const path = '/api/wholesalers/';
    final queryParameters = <String, dynamic>{};
    if (page != null) {
      queryParameters['page'] = page;
    }
    if (pageSize != null) {
      queryParameters['page_size'] = pageSize;
    }
    if (search != null) {
      queryParameters['search'] = search;
    }
    if (userId != null) {
      queryParameters['user_id'] = userId;
    }
    if (regionId != null) {
      queryParameters['region_id'] = regionId;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseData = PaginatedWholesalerResponse.fromJson(response.data as Map<String, dynamic>);
      return Response<PaginatedWholesalerResponse>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// Get Wholesaler
  ///
  /// Get a specific wholesaler by ID.
  /// Requires authentication.
  ///
  Future<Response<WholesalerOut>> getWholesaler(int wholesalerId) async {
    String path = '/api/wholesalers/{wholesaler_id}';
    path = path.replaceAll('{wholesaler_id}', wholesalerId.toString());
    try {
      final response = await _dio.get(path);
      final responseData = WholesalerOut.fromJson(response.data as Map<String, dynamic>);
      return Response<WholesalerOut>(
        data: responseData,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

  /// List Min Charges
  ///
  /// List all min charges.
  /// Requires authentication.
  ///
  Future<Response<List<RegionMinChargeOut>>> listMinCharges({int? regionId, int? wholesalerId}) async {
    const path = '/api/min-charges/';
    final queryParameters = <String, dynamic>{};
    if (regionId != null) {
      queryParameters['region_id'] = regionId;
    }
    if (wholesalerId != null) {
      queryParameters['wholesaler_id'] = wholesalerId;
    }
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      final responseDataList = (response.data as List<dynamic>)
          .map((item) => RegionMinChargeOut.fromJson(item as Map<String, dynamic>))
          .toList();
      return Response<List<RegionMinChargeOut>>(
        data: responseDataList,
        statusCode: response.statusCode,
        statusMessage: response.statusMessage,
        headers: response.headers,
        requestOptions: response.requestOptions,
      );
    } on DioException catch (e) {
      throw ApiExceptionHandler.fromDioException(e);
    } catch (e) {
      throw NetworkException(message: 'Unexpected error: $e');
    }
  }

}
