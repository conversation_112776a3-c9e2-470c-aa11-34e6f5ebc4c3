// Generated model class for CategoryIn
// This file is auto-generated. Do not edit manually.

/// Schema for creating a new category
class CategoryIn {
  final String name;

  final String title;

  final String slug;

  const CategoryIn({
    required this.name,
    required this.title,
    required this.slug,
  });

  factory CategoryIn.fromJson(Map<String, dynamic> json) {
    return CategoryIn(
      name: json['name']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      slug: json['slug']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'title': title,
      'slug': slug,
    };
  }

  @override
  String toString() {
    return 'CategoryIn(name: $name, title: $title, slug: $slug)';
  }

  @override
  int get hashCode {
    return name.hashCode ^ title.hashCode ^ slug.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CategoryIn) return false;
    return name == other.name && title == other.title && slug == other.slug;
  }

}
