// Generated model class for CategoryUpdate
// This file is auto-generated. Do not edit manually.

/// Schema for updating category data
class CategoryUpdate {
  final String? name;

  final String? title;

  final String? slug;

  const CategoryUpdate({
    this.name,
    this.title,
    this.slug,
  });

  factory CategoryUpdate.fromJson(Map<String, dynamic> json) {
    return CategoryUpdate(
      name: json['name'] as String?,
      title: json['title'] as String?,
      slug: json['slug'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'title': title,
      'slug': slug,
    };
  }

  @override
  String toString() {
    return 'CategoryUpdate(name: $name, title: $title, slug: $slug)';
  }

  @override
  int get hashCode {
    return name.hashCode ^ title.hashCode ^ slug.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CategoryUpdate) return false;
    return name == other.name && title == other.title && slug == other.slug;
  }

}
