// Generated model class for CustomUserIn
// This file is auto-generated. Do not edit manually.

/// Schema for creating a new user
class CustomUserIn {
  final String username;

  final String? email;

  final String phone;

  final String password;

  final String? firstName;

  final String? lastName;

  const CustomUserIn({
    required this.username,
    this.email,
    required this.phone,
    required this.password,
    this.firstName,
    this.lastName,
  });

  factory CustomUserIn.fromJson(Map<String, dynamic> json) {
    return CustomUserIn(
      username: json['username']?.toString() ?? '',
      email: json['email'] as String?,
      phone: json['phone']?.toString() ?? '',
      password: json['password']?.toString() ?? '',
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'email': email,
      'phone': phone,
      'password': password,
      'first_name': firstName,
      'last_name': lastName,
    };
  }

  @override
  String toString() {
    return 'CustomUserIn(username: $username, email: $email, phone: $phone, password: $password, first_name: $firstName, last_name: $lastName)';
  }

  @override
  int get hashCode {
    return username.hashCode ^ email.hashCode ^ phone.hashCode ^ password.hashCode ^ firstName.hashCode ^ lastName.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CustomUserIn) return false;
    return username == other.username && email == other.email && phone == other.phone && password == other.password && firstName == other.firstName && lastName == other.lastName;
  }

}
