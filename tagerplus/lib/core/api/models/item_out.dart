// Generated model class for ItemOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

class ItemOut {
  final int id;

  final int wholesalerId;

  final ItemProductOut product;

  final double basePrice;

  final int inventoryCount;

  final int minimumOrderQuantity;

  final int? maximumOrderQuantity;

  const ItemOut({
    required this.id,
    required this.wholesalerId,
    required this.product,
    required this.basePrice,
    required this.inventoryCount,
    required this.minimumOrderQuantity,
    this.maximumOrderQuantity,
  });

  factory ItemOut.fromJson(Map<String, dynamic> json) {
    return ItemOut(
      id: json['id'] as int? ?? 0,
      wholesalerId: json['wholesaler_id'] as int? ?? 0,
      product: ItemProductOut.fromJson(json['product'] as Map<String, dynamic>? ?? {}),
      basePrice: (json['base_price'] as num?)?.toDouble() ?? 0.0,
      inventoryCount: json['inventory_count'] as int? ?? 0,
      minimumOrderQuantity: json['minimum_order_quantity'] as int? ?? 0,
      maximumOrderQuantity: json['maximum_order_quantity'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'wholesaler_id': wholesalerId,
      'product': product.toJson(),
      'base_price': basePrice,
      'inventory_count': inventoryCount,
      'minimum_order_quantity': minimumOrderQuantity,
      'maximum_order_quantity': maximumOrderQuantity,
    };
  }

  @override
  String toString() {
    return 'ItemOut(id: $id, wholesaler_id: $wholesalerId, product: $product, base_price: $basePrice, inventory_count: $inventoryCount, minimum_order_quantity: $minimumOrderQuantity, maximum_order_quantity: $maximumOrderQuantity)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ wholesalerId.hashCode ^ product.hashCode ^ basePrice.hashCode ^ inventoryCount.hashCode ^ minimumOrderQuantity.hashCode ^ maximumOrderQuantity.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ItemOut) return false;
    return id == other.id && wholesalerId == other.wholesalerId && product == other.product && basePrice == other.basePrice && inventoryCount == other.inventoryCount && minimumOrderQuantity == other.minimumOrderQuantity && maximumOrderQuantity == other.maximumOrderQuantity;
  }

}
