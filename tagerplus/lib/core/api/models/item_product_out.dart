// Generated model class for ItemProductOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

class ItemProductOut {
  final int id;

  final String name;

  final String title;

  final String barcode;

  final String slug;

  final String description;

  final String? imageUrl;

  final CategoryOut? category;

  final String unit;

  final double unitCount;

  const ItemProductOut({
    required this.id,
    required this.name,
    required this.title,
    required this.barcode,
    required this.slug,
    required this.description,
    this.imageUrl,
    this.category,
    required this.unit,
    required this.unitCount,
  });

  factory ItemProductOut.fromJson(Map<String, dynamic> json) {
    return ItemProductOut(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      barcode: json['barcode']?.toString() ?? '',
      slug: json['slug']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      imageUrl: json['image_url'] as String?,
      category: json['category'] != null ? CategoryOut.fromJson(json['category'] as Map<String, dynamic>) : null,
      unit: json['unit']?.toString() ?? '',
      unitCount: (json['unit_count'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'title': title,
      'barcode': barcode,
      'slug': slug,
      'description': description,
      'image_url': imageUrl,
      'category': category?.toJson(),
      'unit': unit,
      'unit_count': unitCount,
    };
  }

  @override
  String toString() {
    return 'ItemProductOut(id: $id, name: $name, title: $title, barcode: $barcode, slug: $slug, description: $description, image_url: $imageUrl, category: $category, unit: $unit, unit_count: $unitCount)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ title.hashCode ^ barcode.hashCode ^ slug.hashCode ^ description.hashCode ^ imageUrl.hashCode ^ category.hashCode ^ unit.hashCode ^ unitCount.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ItemProductOut) return false;
    return id == other.id && name == other.name && title == other.title && barcode == other.barcode && slug == other.slug && description == other.description && imageUrl == other.imageUrl && category == other.category && unit == other.unit && unitCount == other.unitCount;
  }

}
