// Generated model class for NotificationMarkReadRequest
// This file is auto-generated. Do not edit manually.

/// Schema for marking notifications as read
class NotificationMarkReadRequest {
  final List<int> notificationIds;

  const NotificationMarkReadRequest({
    required this.notificationIds,
  });

  factory NotificationMarkReadRequest.fromJson(Map<String, dynamic> json) {
    return NotificationMarkReadRequest(
      notificationIds: (json['notification_ids'] as List<dynamic>?)?.map((item) => item as int? ?? 0).toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'notification_ids': notificationIds.map((item) => item).toList(),
    };
  }

  @override
  String toString() {
    return 'NotificationMarkReadRequest(notification_ids: $notificationIds)';
  }

  @override
  int get hashCode {
    return notificationIds.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! NotificationMarkReadRequest) return false;
    return notificationIds == other.notificationIds;
  }

}
