// Generated model class for OrderUpdate
// This file is auto-generated. Do not edit manually.

/// Schema for updating order data
class OrderUpdate {
  final String? status;

  final String? statusReason;

  final String? deliverAt;

  final double? finalCompletedPrice;

  const OrderUpdate({
    this.status,
    this.statusReason,
    this.deliverAt,
    this.finalCompletedPrice,
  });

  factory OrderUpdate.fromJson(Map<String, dynamic> json) {
    return OrderUpdate(
      status: json['status'] as String?,
      statusReason: json['status_reason'] as String?,
      deliverAt: json['deliver_at'] as String?,
      finalCompletedPrice: (json['final_completed_price'] as num?)?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'status_reason': statusReason,
      'deliver_at': deliverAt,
      'final_completed_price': finalCompletedPrice,
    };
  }

  @override
  String toString() {
    return 'OrderUpdate(status: $status, status_reason: $statusReason, deliver_at: $deliverAt, final_completed_price: $finalCompletedPrice)';
  }

  @override
  int get hashCode {
    return status.hashCode ^ statusReason.hashCode ^ deliverAt.hashCode ^ finalCompletedPrice.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! OrderUpdate) return false;
    return status == other.status && statusReason == other.statusReason && deliverAt == other.deliverAt && finalCompletedPrice == other.finalCompletedPrice;
  }

}
