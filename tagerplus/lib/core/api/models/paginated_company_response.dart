// Generated model class for PaginatedCompanyResponse
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Paginated response for companies
class PaginatedCompanyResponse {
  final List<CompanyOut> companies;

  final int totalCount;

  final int page;

  final int pageSize;

  final int totalPages;

  final bool hasNext;

  final bool hasPrevious;

  const PaginatedCompanyResponse({
    required this.companies,
    required this.totalCount,
    required this.page,
    required this.pageSize,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrevious,
  });

  factory PaginatedCompanyResponse.fromJson(Map<String, dynamic> json) {
    return PaginatedCompanyResponse(
      companies: (json['companies'] as List<dynamic>?)?.map((item) => CompanyOut.fromJson(item as Map<String, dynamic>)).toList() ?? [],
      totalCount: json['total_count'] as int? ?? 0,
      page: json['page'] as int? ?? 0,
      pageSize: json['page_size'] as int? ?? 0,
      totalPages: json['total_pages'] as int? ?? 0,
      hasNext: json['has_next'] as bool? ?? false,
      hasPrevious: json['has_previous'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'companies': companies.map((item) => item.toJson()).toList(),
      'total_count': totalCount,
      'page': page,
      'page_size': pageSize,
      'total_pages': totalPages,
      'has_next': hasNext,
      'has_previous': hasPrevious,
    };
  }

  @override
  String toString() {
    return 'PaginatedCompanyResponse(companies: $companies, total_count: $totalCount, page: $page, page_size: $pageSize, total_pages: $totalPages, has_next: $hasNext, has_previous: $hasPrevious)';
  }

  @override
  int get hashCode {
    return companies.hashCode ^ totalCount.hashCode ^ page.hashCode ^ pageSize.hashCode ^ totalPages.hashCode ^ hasNext.hashCode ^ hasPrevious.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! PaginatedCompanyResponse) return false;
    return companies == other.companies && totalCount == other.totalCount && page == other.page && pageSize == other.pageSize && totalPages == other.totalPages && hasNext == other.hasNext && hasPrevious == other.hasPrevious;
  }

}
