// Generated model class for ProductUpdate
// This file is auto-generated. Do not edit manually.

/// Schema for updating product data
class ProductUpdate {
  final String? name;

  final String? title;

  final String? barcode;

  final String? slug;

  final String? description;

  final int? companyId;

  final int? categoryId;

  final String? unit;

  final double? unitCount;

  const ProductUpdate({
    this.name,
    this.title,
    this.barcode,
    this.slug,
    this.description,
    this.companyId,
    this.categoryId,
    this.unit,
    this.unitCount,
  });

  factory ProductUpdate.fromJson(Map<String, dynamic> json) {
    return ProductUpdate(
      name: json['name'] as String?,
      title: json['title'] as String?,
      barcode: json['barcode'] as String?,
      slug: json['slug'] as String?,
      description: json['description'] as String?,
      companyId: json['company_id'] as int?,
      categoryId: json['category_id'] as int?,
      unit: json['unit'] as String?,
      unitCount: (json['unit_count'] as num?)?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'title': title,
      'barcode': barcode,
      'slug': slug,
      'description': description,
      'company_id': companyId,
      'category_id': categoryId,
      'unit': unit,
      'unit_count': unitCount,
    };
  }

  @override
  String toString() {
    return 'ProductUpdate(name: $name, title: $title, barcode: $barcode, slug: $slug, description: $description, company_id: $companyId, category_id: $categoryId, unit: $unit, unit_count: $unitCount)';
  }

  @override
  int get hashCode {
    return name.hashCode ^ title.hashCode ^ barcode.hashCode ^ slug.hashCode ^ description.hashCode ^ companyId.hashCode ^ categoryId.hashCode ^ unit.hashCode ^ unitCount.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ProductUpdate) return false;
    return name == other.name && title == other.title && barcode == other.barcode && slug == other.slug && description == other.description && companyId == other.companyId && categoryId == other.categoryId && unit == other.unit && unitCount == other.unitCount;
  }

}
