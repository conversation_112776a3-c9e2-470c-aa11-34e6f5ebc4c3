// Generated model class for ProductWithWholesalerItemsOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Schema for product with all associated wholesaler items
class ProductWithWholesalerItemsOut {
  final int id;

  final String name;

  final String title;

  final String barcode;

  final String slug;

  final String description;

  final String? imageUrl;

  final CompanyOut? company;

  final CategoryOut? category;

  final String unit;

  final double unitCount;

  final int itemsCount;

  final String createdAt;

  final String updatedAt;

  final List<WholesalerItemWithDetailsOut> wholesalerItems;

  const ProductWithWholesalerItemsOut({
    required this.id,
    required this.name,
    required this.title,
    required this.barcode,
    required this.slug,
    required this.description,
    this.imageUrl,
    this.company,
    this.category,
    required this.unit,
    required this.unitCount,
    required this.itemsCount,
    required this.createdAt,
    required this.updatedAt,
    required this.wholesalerItems,
  });

  factory ProductWithWholesalerItemsOut.fromJson(Map<String, dynamic> json) {
    return ProductWithWholesalerItemsOut(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      barcode: json['barcode']?.toString() ?? '',
      slug: json['slug']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      imageUrl: json['image_url'] as String?,
      company: json['company'] != null ? CompanyOut.fromJson(json['company'] as Map<String, dynamic>) : null,
      category: json['category'] != null ? CategoryOut.fromJson(json['category'] as Map<String, dynamic>) : null,
      unit: json['unit']?.toString() ?? '',
      unitCount: (json['unit_count'] as num?)?.toDouble() ?? 0.0,
      itemsCount: json['items_count'] as int? ?? 0,
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
      wholesalerItems: (json['wholesaler_items'] as List<dynamic>?)?.map((item) => WholesalerItemWithDetailsOut.fromJson(item as Map<String, dynamic>)).toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'title': title,
      'barcode': barcode,
      'slug': slug,
      'description': description,
      'image_url': imageUrl,
      'company': company?.toJson(),
      'category': category?.toJson(),
      'unit': unit,
      'unit_count': unitCount,
      'items_count': itemsCount,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'wholesaler_items': wholesalerItems.map((item) => item.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'ProductWithWholesalerItemsOut(id: $id, name: $name, title: $title, barcode: $barcode, slug: $slug, description: $description, image_url: $imageUrl, company: $company, category: $category, unit: $unit, unit_count: $unitCount, items_count: $itemsCount, created_at: $createdAt, updated_at: $updatedAt, wholesaler_items: $wholesalerItems)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ title.hashCode ^ barcode.hashCode ^ slug.hashCode ^ description.hashCode ^ imageUrl.hashCode ^ company.hashCode ^ category.hashCode ^ unit.hashCode ^ unitCount.hashCode ^ itemsCount.hashCode ^ createdAt.hashCode ^ updatedAt.hashCode ^ wholesalerItems.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ProductWithWholesalerItemsOut) return false;
    return id == other.id && name == other.name && title == other.title && barcode == other.barcode && slug == other.slug && description == other.description && imageUrl == other.imageUrl && company == other.company && category == other.category && unit == other.unit && unitCount == other.unitCount && itemsCount == other.itemsCount && createdAt == other.createdAt && updatedAt == other.updatedAt && wholesalerItems == other.wholesalerItems;
  }

}
