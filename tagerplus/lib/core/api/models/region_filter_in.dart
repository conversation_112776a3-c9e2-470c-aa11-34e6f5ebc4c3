// Generated model class for RegionFilterIn
// This file is auto-generated. Do not edit manually.

/// Schema for region filter input
class RegionFilterIn {
  final int regionId;

  const RegionFilterIn({
    required this.regionId,
  });

  factory RegionFilterIn.fromJson(Map<String, dynamic> json) {
    return RegionFilterIn(
      regionId: json['region_id'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'region_id': regionId,
    };
  }

  @override
  String toString() {
    return 'RegionFilterIn(region_id: $regionId)';
  }

  @override
  int get hashCode {
    return regionId.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! RegionFilterIn) return false;
    return regionId == other.regionId;
  }

}
