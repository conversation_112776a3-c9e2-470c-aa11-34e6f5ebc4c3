// Generated model class for RegionMinChargeOut
// This file is auto-generated. Do not edit manually.

/// Schema for RegionMinCharge output
class RegionMinChargeOut {
  final int id;

  final double minCharge;

  final int minItems;

  final String createdAt;

  final String updatedAt;

  const RegionMinChargeOut({
    required this.id,
    required this.minCharge,
    required this.minItems,
    required this.createdAt,
    required this.updatedAt,
  });

  factory RegionMinChargeOut.fromJson(Map<String, dynamic> json) {
    return RegionMinChargeOut(
      id: json['id'] as int? ?? 0,
      minCharge: (json['min_charge'] as num?)?.toDouble() ?? 0.0,
      minItems: json['min_items'] as int? ?? 0,
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'min_charge': minCharge,
      'min_items': minItems,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  String toString() {
    return 'RegionMinChargeOut(id: $id, min_charge: $minCharge, min_items: $minItems, created_at: $createdAt, updated_at: $updatedAt)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ minCharge.hashCode ^ minItems.hashCode ^ createdAt.hashCode ^ updatedAt.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! RegionMinChargeOut) return false;
    return id == other.id && minCharge == other.minCharge && minItems == other.minItems && createdAt == other.createdAt && updatedAt == other.updatedAt;
  }

}
