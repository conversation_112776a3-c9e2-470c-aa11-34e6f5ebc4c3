// Generated model class for RegionUpdate
// This file is auto-generated. Do not edit manually.

/// Schema for updating region data
class RegionUpdate {
  final String? name;

  final String? type;

  final int? parentId;

  final String? code;

  final String? slug;

  final bool? isActive;

  const RegionUpdate({
    this.name,
    this.type,
    this.parentId,
    this.code,
    this.slug,
    this.isActive,
  });

  factory RegionUpdate.fromJson(Map<String, dynamic> json) {
    return RegionUpdate(
      name: json['name'] as String?,
      type: json['type'] as String?,
      parentId: json['parent_id'] as int?,
      code: json['code'] as String?,
      slug: json['slug'] as String?,
      isActive: json['is_active'] as bool?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'type': type,
      'parent_id': parentId,
      'code': code,
      'slug': slug,
      'is_active': isActive,
    };
  }

  @override
  String toString() {
    return 'RegionUpdate(name: $name, type: $type, parent_id: $parentId, code: $code, slug: $slug, is_active: $isActive)';
  }

  @override
  int get hashCode {
    return name.hashCode ^ type.hashCode ^ parentId.hashCode ^ code.hashCode ^ slug.hashCode ^ isActive.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! RegionUpdate) return false;
    return name == other.name && type == other.type && parentId == other.parentId && code == other.code && slug == other.slug && isActive == other.isActive;
  }

}
