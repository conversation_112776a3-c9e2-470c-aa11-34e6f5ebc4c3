// Generated model class for StoreOrderOut
// This file is auto-generated. Do not edit manually.

/// Simplified store schema for order responses
class StoreOrderOut {
  final int id;

  final String name;

  final String address;

  const StoreOrderOut({
    required this.id,
    required this.name,
    required this.address,
  });

  factory StoreOrderOut.fromJson(Map<String, dynamic> json) {
    return StoreOrderOut(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      address: json['address']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
    };
  }

  @override
  String toString() {
    return 'StoreOrderOut(id: $id, name: $name, address: $address)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ address.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! StoreOrderOut) return false;
    return id == other.id && name == other.name && address == other.address;
  }

}
