// Generated model class for StoreOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Schema for store output
class StoreOut {
  final int id;

  final String name;

  final String description;

  final String address;

  final CustomUserOut owner;

  final RegionOut? city;

  final RegionOut? state;

  final RegionOut? country;

  final String createdAt;

  final String updatedAt;

  const StoreOut({
    required this.id,
    required this.name,
    required this.description,
    required this.address,
    required this.owner,
    this.city,
    this.state,
    this.country,
    required this.createdAt,
    required this.updatedAt,
  });

  factory StoreOut.fromJson(Map<String, dynamic> json) {
    return StoreOut(
      id: json['id'] as int? ?? 0,
      name: json['name']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      address: json['address']?.toString() ?? '',
      owner: CustomUserOut.fromJson(json['owner'] as Map<String, dynamic>? ?? {}),
      city: json['city'] != null ? RegionOut.fromJson(json['city'] as Map<String, dynamic>) : null,
      state: json['state'] != null ? RegionOut.fromJson(json['state'] as Map<String, dynamic>) : null,
      country: json['country'] != null ? RegionOut.fromJson(json['country'] as Map<String, dynamic>) : null,
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'address': address,
      'owner': owner.toJson(),
      'city': city?.toJson(),
      'state': state?.toJson(),
      'country': country?.toJson(),
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  String toString() {
    return 'StoreOut(id: $id, name: $name, description: $description, address: $address, owner: $owner, city: $city, state: $state, country: $country, created_at: $createdAt, updated_at: $updatedAt)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ name.hashCode ^ description.hashCode ^ address.hashCode ^ owner.hashCode ^ city.hashCode ^ state.hashCode ^ country.hashCode ^ createdAt.hashCode ^ updatedAt.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! StoreOut) return false;
    return id == other.id && name == other.name && description == other.description && address == other.address && owner == other.owner && city == other.city && state == other.state && country == other.country && createdAt == other.createdAt && updatedAt == other.updatedAt;
  }

}
