// Generated model class for WholesalerDetailOut
// This file is auto-generated. Do not edit manually.

/// Schema for detailed wholesaler information
class WholesalerDetailOut {
  final int id;

  final String category;

  final String title;

  final String username;

  final String? logoUrl;

  final String? backgroundImageUrl;

  final String createdAt;

  final String updatedAt;

  const WholesalerDetailOut({
    required this.id,
    required this.category,
    required this.title,
    required this.username,
    this.logoUrl,
    this.backgroundImageUrl,
    required this.createdAt,
    required this.updatedAt,
  });

  factory WholesalerDetailOut.fromJson(Map<String, dynamic> json) {
    return WholesalerDetailOut(
      id: json['id'] as int? ?? 0,
      category: json['category']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      username: json['username']?.toString() ?? '',
      logoUrl: json['logo_url'] as String?,
      backgroundImageUrl: json['background_image_url'] as String?,
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category': category,
      'title': title,
      'username': username,
      'logo_url': logoUrl,
      'background_image_url': backgroundImageUrl,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  String toString() {
    return 'WholesalerDetailOut(id: $id, category: $category, title: $title, username: $username, logo_url: $logoUrl, background_image_url: $backgroundImageUrl, created_at: $createdAt, updated_at: $updatedAt)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ category.hashCode ^ title.hashCode ^ username.hashCode ^ logoUrl.hashCode ^ backgroundImageUrl.hashCode ^ createdAt.hashCode ^ updatedAt.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! WholesalerDetailOut) return false;
    return id == other.id && category == other.category && title == other.title && username == other.username && logoUrl == other.logoUrl && backgroundImageUrl == other.backgroundImageUrl && createdAt == other.createdAt && updatedAt == other.updatedAt;
  }

}
