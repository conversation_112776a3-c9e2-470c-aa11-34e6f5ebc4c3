// Generated model class for WholesalerItemOut
// This file is auto-generated. Do not edit manually.

/// Schema for wholesaler item output
class WholesalerItemOut {
  final int id;

  final double basePrice;

  final int inventoryCount;

  final int minimumOrderQuantity;

  final int? maximumOrderQuantity;

  final String priceExpiry;

  final String expiresAt;

  final String createdAt;

  final String updatedAt;

  const WholesalerItemOut({
    required this.id,
    required this.basePrice,
    required this.inventoryCount,
    required this.minimumOrderQuantity,
    this.maximumOrderQuantity,
    required this.priceExpiry,
    required this.expiresAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory WholesalerItemOut.fromJson(Map<String, dynamic> json) {
    return WholesalerItemOut(
      id: json['id'] as int? ?? 0,
      basePrice: (json['base_price'] as num?)?.toDouble() ?? 0.0,
      inventoryCount: json['inventory_count'] as int? ?? 0,
      minimumOrderQuantity: json['minimum_order_quantity'] as int? ?? 0,
      maximumOrderQuantity: json['maximum_order_quantity'] as int?,
      priceExpiry: json['price_expiry']?.toString() ?? '',
      expiresAt: json['expires_at']?.toString() ?? '',
      createdAt: json['created_at']?.toString() ?? '',
      updatedAt: json['updated_at']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'base_price': basePrice,
      'inventory_count': inventoryCount,
      'minimum_order_quantity': minimumOrderQuantity,
      'maximum_order_quantity': maximumOrderQuantity,
      'price_expiry': priceExpiry,
      'expires_at': expiresAt,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  String toString() {
    return 'WholesalerItemOut(id: $id, base_price: $basePrice, inventory_count: $inventoryCount, minimum_order_quantity: $minimumOrderQuantity, maximum_order_quantity: $maximumOrderQuantity, price_expiry: $priceExpiry, expires_at: $expiresAt, created_at: $createdAt, updated_at: $updatedAt)';
  }

  @override
  int get hashCode {
    return id.hashCode ^ basePrice.hashCode ^ inventoryCount.hashCode ^ minimumOrderQuantity.hashCode ^ maximumOrderQuantity.hashCode ^ priceExpiry.hashCode ^ expiresAt.hashCode ^ createdAt.hashCode ^ updatedAt.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! WholesalerItemOut) return false;
    return id == other.id && basePrice == other.basePrice && inventoryCount == other.inventoryCount && minimumOrderQuantity == other.minimumOrderQuantity && maximumOrderQuantity == other.maximumOrderQuantity && priceExpiry == other.priceExpiry && expiresAt == other.expiresAt && createdAt == other.createdAt && updatedAt == other.updatedAt;
  }

}
