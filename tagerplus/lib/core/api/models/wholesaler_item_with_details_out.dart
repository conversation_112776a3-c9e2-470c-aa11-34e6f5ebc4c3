// Generated model class for WholesalerItemWithDetailsOut
// This file is auto-generated. Do not edit manually.

import 'models.dart';

/// Schema combining wholesaler item with wholesaler details and regional pricing
class WholesalerItemWithDetailsOut {
  final WholesalerItemOut item;

  final WholesalerDetailOut wholesaler;

  final RegionMinChargeOut? regionalPricing;

  const WholesalerItemWithDetailsOut({
    required this.item,
    required this.wholesaler,
    this.regionalPricing,
  });

  factory WholesalerItemWithDetailsOut.fromJson(Map<String, dynamic> json) {
    return WholesalerItemWithDetailsOut(
      item: WholesalerItemOut.fromJson(json['item'] as Map<String, dynamic>? ?? {}),
      wholesaler: WholesalerDetailOut.fromJson(json['wholesaler'] as Map<String, dynamic>? ?? {}),
      regionalPricing: json['regional_pricing'] != null ? RegionMinChargeOut.fromJson(json['regional_pricing'] as Map<String, dynamic>) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'item': item.toJson(),
      'wholesaler': wholesaler.toJson(),
      'regional_pricing': regionalPricing?.toJson(),
    };
  }

  @override
  String toString() {
    return 'WholesalerItemWithDetailsOut(item: $item, wholesaler: $wholesaler, regional_pricing: $regionalPricing)';
  }

  @override
  int get hashCode {
    return item.hashCode ^ wholesaler.hashCode ^ regionalPricing.hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! WholesalerItemWithDetailsOut) return false;
    return item == other.item && wholesaler == other.wholesaler && regionalPricing == other.regionalPricing;
  }

}
