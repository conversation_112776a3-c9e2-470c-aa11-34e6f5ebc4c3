import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:tagerplus/services/auth.dart' as old_auth;
import '../../../core/api/api.dart';
import '../../../services/api.dart';
import 'package:tagerplus/pages/store/create/create.dart';

class LoginController extends GetxController {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  final RxBool isPasswordHidden = true.obs;
  final RxBool isLoading = false.obs;

  void togglePasswordVisibility() {
    isPasswordHidden.value = !isPasswordHidden.value;
  }

  String? validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'please_enter_phone'.tr;
    }
    if (value.trim().length < 8) {
      return 'enter_valid_phone'.tr;
    }
    return null;
  }

  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'please_enter_password'.tr;
    }
    if (value.length < 6) {
      return 'password_min_length'.tr;
    }
    return null;
  }

  Future<void> login() async {
    if (isLoading.value) return;
    final isValid = formKey.currentState?.validate() ?? false;
    if (!isValid) return;

    isLoading.value = true;
    try {
      final loginRequest = LoginRequest(
        phone: phoneNumberController.text.trim(),
        password: passwordController.text,
      );

      final loginResponse = await ApiService.to.apiClient.auth.signin(
        loginRequest,
      );

      if (loginResponse.statusCode == 200 && loginResponse.data != null) {
        Get.snackbar('success'.tr, 'logged_in_successfully'.tr);

        // Check if user has stores using the old auth service
        if (old_auth.AuthService.to.hasStores) {
          // Navigate to home page
          Get.offAll(
            () => const Scaffold(
              body: Center(child: Text('Home Page - Coming Soon')),
            ),
          );
        } else {
          // Navigate to store creation
          Get.offAll(() => const StoreCreatePage());
        }
      } else {
        Get.snackbar('error'.tr, 'login_failed'.tr);
      }
    } on Exception catch (e) {
      String errorMessage = 'login_failed'.tr;

      if (e.toString().contains('Unauthorized')) {
        errorMessage = 'invalid_credentials'.tr;
      } else if (e.toString().contains('Connection')) {
        errorMessage = 'network_error'.tr;
      } else if (e.toString().contains('Server')) {
        errorMessage = 'server_error'.tr;
      }

      if (kDebugMode) {
        print('Login error: $e');
      }

      Get.snackbar('error'.tr, errorMessage);
    } catch (e) {
      if (kDebugMode) {
        print('Unexpected login error: $e');
      }
      Get.snackbar('error'.tr, 'login_failed'.tr);
    } finally {
      isLoading.value = false;
    }
  }

  @override
  void onClose() {
    phoneNumberController.dispose();
    passwordController.dispose();
    super.onClose();
  }
}
