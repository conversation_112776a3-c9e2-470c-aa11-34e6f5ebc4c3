import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_icons.dart';
import 'package:tagerplus/components/app_navigation_bar.dart';
import 'package:tagerplus/pages/home/<USER>';
import 'package:tagerplus/pages/search/search.dart';
import 'package:tagerplus/pages/home/<USER>';
import 'package:tagerplus/pages/search/controller.dart';

class EntryPointUI extends StatefulWidget {
  const EntryPointUI({super.key});

  @override
  State<EntryPointUI> createState() => _EntryPointUIState();
}

class _EntryPointUIState extends State<EntryPointUI> {
  /// Current Page Index
  int currentIndex = 0;

  @override
  void initState() {
    super.initState();
    // Initialize controllers
    Get.put(HomeController());
    Get.put(SearchPageController());
  }

  /// On bottom navigation tap
  void onBottomNavigationTap(int index) {
    setState(() {
      currentIndex = index;
    });
  }

  /// All the pages
  List<Widget> get pages => [
    const HomePage(), // 0 - Home
    const SearchPage(), // 1 - Search
    _buildPlaceholderPage(
      'cart'.tr,
      Icons.shopping_cart,
    ), // 2 - Cart (placeholder)
    _buildPlaceholderPage(
      'saved'.tr,
      Icons.bookmark,
    ), // 3 - Saved (placeholder)
    _buildPlaceholderPage(
      'profile'.tr,
      Icons.person,
    ), // 4 - Profile (placeholder)
  ];

  Widget _buildPlaceholderPage(String title, IconData icon) {
    return Scaffold(
      appBar: AppBar(title: Text(title), centerTitle: true),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'قريباً',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(index: currentIndex, children: pages),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          onBottomNavigationTap(2); // Navigate to cart
        },
        backgroundColor: AppColors.primary,
        child: SvgPicture.asset(
          AppIcons.cart,
          colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: AppBottomNavigationBar(
        currentIndex: currentIndex,
        onNavTap: onBottomNavigationTap,
      ),
    );
  }
}
