import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:tagerplus/services/api.dart';
import 'package:tagerplus/services/auth.dart';
import 'package:tagerplus/pages/wholesaler_details/wholesaler_details.dart';

class HomeController extends GetxController {
  // Private reactive variables
  final RxBool _isLoadingWholesalers = false.obs;
  final RxBool _isLoadingLatestProducts = false.obs;
  final RxString _errorMessage = ''.obs;

  final RxList<WholesalerOut> _wholesalers = <WholesalerOut>[].obs;
  final RxList<ProductOut> _latestProducts = <ProductOut>[].obs;

  // Getters (public access to private variables)
  bool get isLoadingWholesalers => _isLoadingWholesalers.value;
  bool get isLoadingLatestProducts => _isLoadingLatestProducts.value;
  String get errorMessage => _errorMessage.value;

  List<WholesalerOut> get wholesalers => _wholesalers;
  List<ProductOut> get latestProducts => _latestProducts;

  // Setters (controlled modification)
  set isLoadingWholesalers(bool value) => _isLoadingWholesalers.value = value;
  set isLoadingLatestProducts(bool value) =>
      _isLoadingLatestProducts.value = value;
  set errorMessage(String value) => _errorMessage.value = value;

  @override
  void onInit() {
    super.onInit();
    loadHomeData();
  }

  /// Load all home data
  Future<void> loadHomeData({bool forceRefresh = false}) async {
    await Future.wait([
      loadWholesalers(forceRefresh: forceRefresh),
      loadLatestProducts(forceRefresh: forceRefresh),
    ]);
  }

  /// Load wholesalers
  Future<void> loadWholesalers({bool forceRefresh = false}) async {
    if (isLoadingWholesalers && !forceRefresh) return;

    isLoadingWholesalers = true;
    errorMessage = '';

    try {
      final response = await ApiService.to.apiClient.wholesalers
          .listWholesalers(
            page: 1,
            pageSize: 20,
            regionId: AuthService.to.stores.first.city?.id,
          );

      if (response.data != null) {
        _wholesalers.value = response.data!.wholesalers;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading wholesalers: $e');
      }
      errorMessage = 'error_loading_wholesalers'.tr;
    } finally {
      isLoadingWholesalers = false;
    }
  }

  /// Load latest products
  Future<void> loadLatestProducts({bool forceRefresh = false}) async {
    if (isLoadingLatestProducts && !forceRefresh) return;

    isLoadingLatestProducts = true;

    try {
      final response = await ApiService.to.apiClient.home.getHomeProducts(
        page: 1,
        pageSize: 8, // Show 8 products in 2x4 grid
      );

      if (response.data != null) {
        _latestProducts.value = response.data!.products;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading latest products: $e');
      }
    } finally {
      isLoadingLatestProducts = false;
    }
  }

  /// Refresh all data
  @override
  Future<void> refresh() async {
    await loadHomeData(forceRefresh: true);
  }

  /// Navigate to wholesaler products
  void navigateToWholesaler(WholesalerOut wholesaler) {
    Get.to(
      () => const WholesalerDetailsPage(),
      arguments: {'wholesaler': wholesaler},
    );
  }

  /// Navigate to all wholesalers
  void navigateToAllWholesalers() {
    // TODO: Implement navigation to all wholesalers page
    Get.snackbar('wholesalers'.tr, 'view_all'.tr);
  }

  /// Navigate to latest products
  void navigateToLatestProducts() {
    // TODO: Implement navigation to latest products page
    Get.snackbar('latest_products'.tr, 'view_all'.tr);
  }
}
