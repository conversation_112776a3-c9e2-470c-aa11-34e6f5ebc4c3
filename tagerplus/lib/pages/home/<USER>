import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/core/constants/app_icons.dart';
import 'package:tagerplus/pages/home/<USER>';
import 'package:tagerplus/pages/home/<USER>/components.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<HomeController>();

    return Scaffold(
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: controller.refresh,
          child: CustomScrollView(
            slivers: [
              // App Bar
              SliverAppBar(
                // leading: Padding(
                //   padding: const EdgeInsets.only(left: 8),
                //   child: ElevatedButton(
                //     onPressed: () {
                //       // TODO: Implement drawer/menu navigation
                //       Get.snackbar('menu'.tr, 'Menu coming soon');
                //     },
                //     style: ElevatedButton.styleFrom(
                //       backgroundColor: const Color(0xFFF2F6F3),
                //       shape: const CircleBorder(),
                //     ),
                //     child: SvgPicture.asset(AppIcons.menu),
                //   ),
                // ),
                floating: true,
                title: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image.asset(AppIcons.logoCropped, height: 32),
                    const SizedBox(width: 11),
                    Text(
                      'تاجر بلس',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                actions: [
                  Padding(
                    padding: const EdgeInsets.only(right: 8, top: 4, bottom: 4),
                    child: ElevatedButton(
                      onPressed: () {
                        // Navigate to search page
                        Get.toNamed('/search');
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFF2F6F3),
                        shape: const CircleBorder(),
                      ),
                      child: SvgPicture.asset(AppIcons.search),
                    ),
                  ),
                ],
              ),

              // Store Selector (placeholder for now)
              const SliverToBoxAdapter(child: StoreSelectorWidget()),

              // Ad Space (placeholder for now)
              // const SliverToBoxAdapter(child: AdSpaceWidget()),

              // Wholesalers Grid
              const SliverToBoxAdapter(child: WholesalersGridWidget()),

              // Latest Products
              const SliverPadding(
                padding: EdgeInsets.symmetric(vertical: AppDefaults.padding),
                sliver: SliverToBoxAdapter(child: LatestProductsWidget()),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
