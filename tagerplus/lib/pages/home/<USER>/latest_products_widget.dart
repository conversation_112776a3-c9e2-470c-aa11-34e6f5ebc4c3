import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/components/title_and_action_button.dart';
import 'package:tagerplus/components/product_tile_square.dart';
import 'package:tagerplus/pages/home/<USER>';

class LatestProductsWidget extends StatelessWidget {
  const LatestProductsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<HomeController>();

    return Column(
      children: [
        TitleAndActionButton(
          title: 'latest_products'.tr,
          onTap: controller.navigateToLatestProducts,
        ),
        const SizedBox(height: AppDefaults.padding),
        Obx(() => _buildProductsGrid(context, controller)),
      ],
    );
  }

  Widget _buildProductsGrid(BuildContext context, HomeController controller) {
    if (controller.isLoadingLatestProducts) {
      return Container(
        height: 200,
        alignment: Alignment.center,
        child: const CircularProgressIndicator(),
      );
    }

    if (controller.latestProducts.isEmpty) {
      return Container(
        height: 200,
        alignment: Alignment.center,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_bag_outlined,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد منتجات متاحة',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'سيتم إضافة المنتجات قريباً',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          childAspectRatio: 0.6,
        ),
        itemCount: controller.latestProducts.length,
        itemBuilder: (context, index) {
          final product = controller.latestProducts[index];
          return ProductTileSquare(
            product: product,
            onTap: () {
              // TODO: Navigate to product details
              Get.snackbar('product_details'.tr, product.title);
            },
          );
        },
      ),
    );
  }
}
