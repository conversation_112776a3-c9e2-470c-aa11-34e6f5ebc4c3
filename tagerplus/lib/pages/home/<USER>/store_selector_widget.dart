import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/services/auth.dart';

class StoreSelectorWidget extends StatelessWidget {
  const StoreSelectorWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final currentStore = AuthService.to.currentStore.value;

      if (currentStore == null) {
        return const SizedBox.shrink();
      }

      return Container(
        margin: const EdgeInsets.all(AppDefaults.padding),
        padding: const EdgeInsets.symmetric(
          horizontal: AppDefaults.padding,
          vertical: AppDefaults.padding / 2,
        ),
        decoration: BoxDecoration(
          color: AppColors.coloredBackground,
          borderRadius: AppDefaults.borderRadius,
          border: Border.all(
            color: AppColors.primary.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(Icons.store, color: AppColors.primary, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    currentStore.name,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  if (currentStore.address.isNotEmpty)
                    Text(
                      currentStore.address,
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
            Icon(Icons.keyboard_arrow_down, color: AppColors.primary, size: 20),
          ],
        ),
      );
    });
  }
}
