import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/components/title_and_action_button.dart';
import 'package:tagerplus/components/wholesaler_card.dart';
import 'package:tagerplus/pages/home/<USER>';

class WholesalersGridWidget extends StatelessWidget {
  const WholesalersGridWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<HomeController>();

    return Column(
      children: [
        TitleAndActionButton(
          title: 'wholesalers'.tr,
          onTap: controller.navigateToAllWholesalers,
        ),
        const SizedBox(height: AppDefaults.padding),
        Obx(() => _buildWholesalerGrid(context, controller)),
      ],
    );
  }

  Widget _buildWholesalerGrid(BuildContext context, HomeController controller) {
    if (controller.isLoadingWholesalers) {
      return Container(
        height: 200,
        alignment: Alignment.center,
        child: const CircularProgressIndicator(),
      );
    }

    if (controller.errorMessage.isNotEmpty) {
      return Container(
        height: 200,
        alignment: Alignment.center,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'error_loading_wholesalers'.tr,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              controller.errorMessage,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => controller.loadWholesalers(forceRefresh: true),
              child: Text('retry'.tr),
            ),
          ],
        ),
      );
    }

    if (controller.wholesalers.isEmpty) {
      return Container(
        height: 200,
        alignment: Alignment.center,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.store_outlined,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'no_wholesalers_available'.tr,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'wholesalers_coming_soon'.tr,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
          ],
        ),
      );
    }

    // Show up to 6 wholesalers in a 2x3 grid
    final displayWholesalers = controller.wholesalers.take(6).toList();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 1.2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        itemCount: displayWholesalers.length,
        itemBuilder: (context, index) {
          final wholesaler = displayWholesalers[index];
          return WholesalerCard(
            wholesaler: wholesaler,
            onTap: () => controller.navigateToWholesaler(wholesaler),
            // TODO: Implement cart functionality
            hasCart: false,
            cartItemCount: 0,
          );
        },
      ),
    );
  }
}
