import 'package:flutter/material.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/core/api/models/product_out.dart';
import 'package:tagerplus/components/network_image.dart';

class ProductImageSection extends StatelessWidget {
  const ProductImageSection({
    super.key,
    required this.product,
  });

  final ProductOut product;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Image
          Container(
            height: 300,
            width: double.infinity,
            margin: const EdgeInsets.all(AppDefaults.padding),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: AppDefaults.borderRadius,
            ),
            child: product.imageUrl != null && product.imageUrl!.isNotEmpty
                ? NetworkImageWithLoader(
                    product.imageUrl!,
                    fit: BoxFit.contain,
                    borderRadius: AppDefaults.borderRadius,
                  )
                : Icon(
                    Icons.image_not_supported_outlined,
                    size: 80,
                    color: Colors.grey[400],
                  ),
          ),
          
          // Product Title
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
            child: Text(
              product.title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
            ),
          ),
          
          const SizedBox(height: AppDefaults.padding),
        ],
      ),
    );
  }
}
