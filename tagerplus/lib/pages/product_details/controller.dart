import 'package:get/get.dart';
import 'package:tagerplus/core/api/models/models.dart';

class ProductDetailsController extends GetxController {
  // Private reactive variables
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final Rx<ProductOut?> _product = Rx<ProductOut?>(null);
  // Getters (public access to private variables)
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  ProductOut? get product => _product.value;

  // Setters (controlled modification)
  set isLoading(bool value) => _isLoading.value = value;
  set errorMessage(String value) => _errorMessage.value = value;
  set product(ProductOut? value) => _product.value = value;

  @override
  void onInit() {
    super.onInit();
    _handleInitialArguments();
  }

  /// Handle initial arguments from navigation
  void _handleInitialArguments() {
    final args = Get.arguments as Map<String, dynamic>?;
    if (args != null) {
      final productArg = args['product'] as ProductOut?;
      if (productArg != null) {
        product = productArg;
      }
    }
  }
}
