import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:tagerplus/services/api.dart';

class SearchPageController extends GetxController {
  // Text controller for search input
  final TextEditingController searchController = TextEditingController();

  // Private reactive variables
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxString _currentQuery = ''.obs;
  final RxList<ProductOut> _searchResults = <ProductOut>[].obs;

  // Getters (public access to private variables)
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  String get currentQuery => _currentQuery.value;
  List<ProductOut> get searchResults => _searchResults;

  // Setters (controlled modification)
  set isLoading(bool value) => _isLoading.value = value;
  set errorMessage(String value) => _errorMessage.value = value;
  set currentQuery(String value) => _currentQuery.value = value;

  // Search suggestions
  final List<String> searchSuggestions = [
    'بيبسي',
    'كوكاكولا',
    'مياه',
    'مياه معدنية',
    'جبنة دومتي',
    'لبن',
    'زيت',
    'أرز',
    'سكر',
    'شاي',
  ];

  @override
  void onInit() {
    super.onInit();
    // Handle initial arguments if any
    _handleInitialArguments();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  /// Handle initial arguments from navigation
  void _handleInitialArguments() {
    final args = Get.arguments as Map<String, dynamic>?;
    if (args != null) {
      final query = args['query'] as String?;
      final results = args['results'] as List<ProductOut>?;

      if (query != null) {
        currentQuery = query;
        searchController.text = query;

        if (results != null) {
          _searchResults.value = results;
        } else {
          performSearch(query);
        }
      }
    }
  }

  /// Perform search with the given query
  Future<void> performSearch(String query) async {
    if (query.trim().isEmpty) return;

    isLoading = true;
    errorMessage = '';
    currentQuery = query.trim();

    try {
      final response = await ApiService.to.apiClient.home.getHomeProducts(
        search: query.trim(),
        pageSize: 50,
      );

      if (response.data != null) {
        _searchResults.value = response.data!.products;
      } else {
        _searchResults.value = [];
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error searching products: $e');
      }
      errorMessage = _getErrorMessage(e);
      _searchResults.value = [];
    } finally {
      isLoading = false;
    }
  }

  /// Search with current text controller value
  Future<void> searchWithCurrentText() async {
    final query = searchController.text.trim();
    if (query.isNotEmpty) {
      await performSearch(query);
    }
  }

  /// Clear search results and query
  void clearSearch() {
    searchController.clear();
    currentQuery = '';
    _searchResults.clear();
    errorMessage = '';
  }

  /// Use a search suggestion
  void useSuggestion(String suggestion) {
    searchController.text = suggestion;
    performSearch(suggestion);
  }

  /// Retry the last search
  void retrySearch() {
    if (currentQuery.isNotEmpty) {
      performSearch(currentQuery);
    }
  }

  /// Convert exception to user-friendly Arabic error message
  String _getErrorMessage(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('timeout') || errorString.contains('connection')) {
      return 'انتهت مهلة الاتصال. تحقق من اتصالك بالإنترنت وحاول مرة أخرى.';
    } else if (errorString.contains('network') ||
        errorString.contains('internet')) {
      return 'لا يوجد اتصال بالإنترنت. تحقق من اتصالك وحاول مرة أخرى.';
    } else if (errorString.contains('authentication') ||
        errorString.contains('401')) {
      return 'انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى.';
    } else if (errorString.contains('server') || errorString.contains('500')) {
      return 'خطأ في الخادم. يرجى المحاولة لاحقاً.';
    } else if (errorString.contains('not found') ||
        errorString.contains('404')) {
      return 'لم يتم العثور على المنتجات المطلوبة.';
    } else {
      return 'search_error_retry'.tr;
    }
  }
}
