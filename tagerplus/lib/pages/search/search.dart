import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/core/constants/app_icons.dart';
import 'package:tagerplus/components/product_tile_square.dart';
import 'package:tagerplus/pages/search/controller.dart';

class SearchPage extends StatelessWidget {
  const SearchPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SearchPageController>();

    return Scaffold(
      appBar: AppBar(
        leading: Padding(
          padding: const EdgeInsets.only(left: 8),
          child: ElevatedButton(
            onPressed: () {
              // TODO: Implement drawer/menu navigation
              Get.snackbar('menu'.tr, 'Menu coming soon');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFF2F6F3),
              shape: const CircleBorder(),
            ),
            child: SvgPicture.asset(AppIcons.menu),
          ),
        ),
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(AppIcons.logoCropped, height: 32),
            const SizedBox(width: 11),
            Text(
              'تاجر بلس',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // Search Input
          Padding(
            padding: const EdgeInsets.all(AppDefaults.padding),
            child: TextField(
              controller: controller.searchController,
              decoration: InputDecoration(
                hintText: 'search_products'.tr,
                suffixIcon: Padding(
                  padding: const EdgeInsets.all(AppDefaults.padding),
                  child: SvgPicture.asset(AppIcons.search),
                ),
                suffixIconConstraints: const BoxConstraints(),
                border: OutlineInputBorder(
                  borderRadius: AppDefaults.borderRadius,
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: AppDefaults.borderRadius,
                  borderSide: const BorderSide(color: AppColors.primary),
                ),
              ),
              textInputAction: TextInputAction.search,
              onSubmitted: (value) => controller.performSearch(value),
            ),
          ),

          // Content
          Expanded(child: Obx(() => _buildContent(context, controller))),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context, SearchPageController controller) {
    if (controller.isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'searching'.tr,
              style: const TextStyle(color: Colors.grey, fontSize: 16),
            ),
          ],
        ),
      );
    }

    if (controller.errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              controller.errorMessage,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: controller.retrySearch,
              child: Text('retry'.tr),
            ),
          ],
        ),
      );
    }

    // Show search suggestions when no search has been performed
    if (controller.currentQuery.isEmpty && controller.searchResults.isEmpty) {
      return _buildSearchSuggestions(context, controller);
    }

    if (controller.searchResults.isEmpty &&
        controller.currentQuery.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.search_off, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              'no_results_found'.tr,
              style: const TextStyle(fontSize: 18, color: Colors.grey),
            ),
            const SizedBox(height: 8),
            Text(
              'try_different_keywords'.tr,
              style: const TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return _buildSearchResults(context, controller);
  }

  Widget _buildSearchSuggestions(
    BuildContext context,
    SearchPageController controller,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
          child: Text(
            'search_suggestions'.tr,
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
        ),
        Expanded(
          child: ListView.separated(
            padding: const EdgeInsets.only(top: 16),
            itemBuilder: (context, index) {
              final suggestion = controller.searchSuggestions[index];
              return _SearchSuggestionTile(
                searchTerm: suggestion,
                onTap: () => controller.useSuggestion(suggestion),
              );
            },
            separatorBuilder: (context, index) => const Divider(thickness: 0.1),
            itemCount: controller.searchSuggestions.length,
          ),
        ),
      ],
    );
  }

  Widget _buildSearchResults(
    BuildContext context,
    SearchPageController controller,
  ) {
    return Column(
      children: [
        // Results count
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: AppDefaults.padding),
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              'products_found'.trParams({
                'count': controller.searchResults.length.toString(),
              }),
              style: Theme.of(
                context,
              ).textTheme.bodyLarge?.copyWith(color: Colors.black),
            ),
          ),
        ),

        // Results grid
        Expanded(
          child: GridView.builder(
            padding: const EdgeInsets.all(AppDefaults.padding),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: 0.66,
            ),
            itemCount: controller.searchResults.length,
            itemBuilder: (context, index) {
              final product = controller.searchResults[index];
              return ProductTileSquare(
                product: product,
                onTap: () {
                  // TODO: Navigate to product details
                  Get.snackbar('product_details'.tr, product.title);
                },
              );
            },
          ),
        ),
      ],
    );
  }
}

class _SearchSuggestionTile extends StatelessWidget {
  const _SearchSuggestionTile({required this.searchTerm, required this.onTap});

  final String searchTerm;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        child: Row(
          children: [
            Text(searchTerm, style: Theme.of(context).textTheme.bodyMedium),
            const Spacer(),
            SvgPicture.asset(AppIcons.arrowForward),
          ],
        ),
      ),
    );
  }
}
