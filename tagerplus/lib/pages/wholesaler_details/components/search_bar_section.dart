import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/core/constants/app_icons.dart';
import 'package:tagerplus/pages/wholesaler_details/controller.dart';

class SearchBarSection extends StatelessWidget {
  const SearchBarSection({super.key, required this.controller});

  final WholesalerDetailsController controller;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(AppDefaults.padding),
      child: TextField(
        controller: controller.searchController,
        decoration: InputDecoration(
          hintText: 'search_in_products'.tr,
          prefixIcon: Padding(
            padding: const EdgeInsets.all(12),
            child: SvgPicture.asset(AppIcons.search, width: 20, height: 20),
          ),
          suffixIcon: controller.searchController.text.isNotEmpty
              ? IconButton(
                  onPressed: controller.clearSearch,
                  icon: const Icon(Icons.clear),
                )
              : const SizedBox.shrink(),

          border: OutlineInputBorder(
            borderRadius: AppDefaults.borderRadius,
            borderSide: BorderSide(color: Colors.grey[300]!),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: AppDefaults.borderRadius,
            borderSide: const BorderSide(color: AppColors.primary),
          ),
          filled: true,
          fillColor: Colors.grey[50],
        ),
        textInputAction: TextInputAction.search,
        onChanged: controller.searchProducts,
        onSubmitted: controller.searchProducts,
      ),
    );
  }
}
