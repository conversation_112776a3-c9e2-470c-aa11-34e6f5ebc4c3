import 'package:flutter/material.dart';

import 'package:tagerplus/core/constants/app_defaults.dart';
import 'package:tagerplus/core/api/models/wholesaler_out.dart';
import 'package:tagerplus/components/network_image.dart';

class WholesalerHeaderSection extends StatelessWidget {
  const WholesalerHeaderSection({super.key, required this.wholesaler});

  final WholesalerOut wholesaler;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: Colors.white,
      padding: const EdgeInsets.all(AppDefaults.padding),
      child: Column(
        children: [
          // Wholesaler Logo
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!, width: 1),
            ),
            child: wholesaler.logoUrl != null && wholesaler.logoUrl!.isNotEmpty
                ? NetworkImageWithLoader(
                    wholesaler.logoUrl!,
                    fit: BoxFit.contain,
                    borderRadius: BorderRadius.circular(12),
                  )
                : Icon(Icons.store_outlined, size: 40, color: Colors.grey[400]),
          ),

          const SizedBox(height: 12),

          // Wholesaler Name
          Text(
            wholesaler.title,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),

          // Wholesaler Description
          if (wholesaler.description.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              wholesaler.description,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],

          const SizedBox(height: 16),

          // Divider
          Divider(color: Colors.grey[200], thickness: 1),
        ],
      ),
    );
  }
}
