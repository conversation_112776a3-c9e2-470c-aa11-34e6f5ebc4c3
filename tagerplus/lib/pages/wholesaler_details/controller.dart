import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tagerplus/core/api/models/models.dart';
import 'package:tagerplus/services/api.dart';
import 'package:tagerplus/services/auth.dart';
import 'package:tagerplus/pages/product_details/product_details.dart';

enum SortOption { name, price }

class WholesalerDetailsController extends GetxController {
  // Text controller for search input
  final TextEditingController searchController = TextEditingController();

  // Private reactive variables
  final RxBool _isLoading = false.obs;
  final RxString _errorMessage = ''.obs;
  final Rx<WholesalerOut?> _wholesaler = Rx<WholesalerOut?>(null);
  final RxList<ItemOut> _products = <ItemOut>[].obs;
  final RxList<ItemOut> _filteredProducts = <ItemOut>[].obs;
  final Rx<SortOption> _sortOption = SortOption.name.obs;
  final RxDouble _regionMinimumCharge = 0.0.obs;
  final RxList<String> _categories = <String>[].obs;
  final RxString _selectedCategory = 'all_categories'.obs;

  // Getters (public access to private variables)
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  WholesalerOut? get wholesaler => _wholesaler.value;
  List<ItemOut> get products => _products;
  List<ItemOut> get filteredProducts => _filteredProducts;
  SortOption get sortOption => _sortOption.value;
  double get regionMinimumCharge => _regionMinimumCharge.value;
  List<String> get categories => _categories;
  String get selectedCategory => _selectedCategory.value;

  // Setters (controlled modification)
  set isLoading(bool value) => _isLoading.value = value;
  set errorMessage(String value) => _errorMessage.value = value;
  set wholesaler(WholesalerOut? value) => _wholesaler.value = value;
  set sortOption(SortOption value) => _sortOption.value = value;

  @override
  void onInit() {
    super.onInit();
    _handleInitialArguments();
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  /// Handle initial arguments from navigation
  void _handleInitialArguments() {
    final args = Get.arguments as Map<String, dynamic>?;
    if (args != null) {
      final wholesalerArg = args['wholesaler'] as WholesalerOut?;
      final wholesalerId = args['wholesalerId'] as String?;

      if (wholesalerArg != null) {
        wholesaler = wholesalerArg;
        loadWholesalerProducts(wholesalerArg.id.toString());
        loadRegionMinimumCharge(wholesalerArg.id.toString());
      } else if (wholesalerId != null) {
        loadWholesalerDetails(wholesalerId);
      }
    }
  }

  /// Load wholesaler details by ID
  Future<void> loadWholesalerDetails(String wholesalerId) async {
    isLoading = true;
    errorMessage = '';

    try {
      // TODO: Implement API call to get wholesaler details by ID
      if (kDebugMode) {
        print('Loading wholesaler details for ID: $wholesalerId');
      }

      // For now, load products directly
      await loadWholesalerProducts(wholesalerId);
      await loadRegionMinimumCharge(wholesalerId);
    } catch (e) {
      if (kDebugMode) {
        print('Error loading wholesaler details: $e');
      }
      errorMessage = 'خطأ في تحميل تفاصيل التاجر';
    } finally {
      isLoading = false;
    }
  }

  /// Load products from the wholesaler
  Future<void> loadWholesalerProducts(String wholesalerId) async {
    isLoading = true;
    errorMessage = '';

    try {
      final response = await ApiService.to.apiClient.items.listWholesalerItems(
        int.parse(wholesalerId),
      );

      if (response.data != null) {
        _products.value = response.data!.items;
        _extractCategories();
        _applyFiltersAndSort();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading wholesaler products: $e');
      }
      errorMessage = 'خطأ في تحميل منتجات التاجر';
    } finally {
      isLoading = false;
    }
  }

  /// Load region minimum charge
  Future<void> loadRegionMinimumCharge(String wholesalerId) async {
    try {
      // Get the region ID from the user's first store
      final regionId = AuthService.to.stores.first.city?.id;

      if (regionId != null) {
        final response = await ApiService.to.apiClient.wholesalers
            .listMinCharges(
              regionId: regionId,
              wholesalerId: int.parse(wholesalerId),
            );
        if (response.data != null) {
          _regionMinimumCharge.value = response.data!.first.minCharge;
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading region minimum charge: $e');
      }
    }
  }

  /// Search products
  void searchProducts(String query) {
    searchController.text = query;
    _applyFiltersAndSort();
  }

  /// Extract categories from products
  void _extractCategories() {
    final categorySet = <String>{};

    // Extract categories from products (mock categories based on product names)
    for (final product in _products) {
      final category = product.product.category?.name ?? 'other';
      categorySet.add(category);
    }

    // Add "All Categories" at the beginning
    final categoriesList = ['all_categories'];
    categoriesList.addAll(categorySet.toList()..sort());

    _categories.value = categoriesList;
  }

  /// Select category filter
  void selectCategory(String category) {
    _selectedCategory.value = category;
    _applyFiltersAndSort();
  }

  /// Apply filters and sorting
  void _applyFiltersAndSort() {
    List<ItemOut> filtered = List.from(_products);

    // Apply category filter
    if (selectedCategory != 'all_categories') {
      filtered = filtered.where((product) {
        return product.product.category?.name == selectedCategory;
      }).toList();
    }

    // Apply search filter
    final query = searchController.text.trim().toLowerCase();
    if (query.isNotEmpty) {
      filtered = filtered.where((product) {
        return product.product.title.toLowerCase().contains(query) ||
            product.product.description.toLowerCase().contains(query);
      }).toList();
    }

    // Apply sorting
    switch (sortOption) {
      case SortOption.name:
        filtered.sort((a, b) => a.product.title.compareTo(b.product.title));
        break;
      case SortOption.price:
        // Since ProductOut doesn't have price, we'll sort by title for now
        // In a real implementation, you'd sort by price from pricing data
        filtered.sort((a, b) => a.product.title.compareTo(b.product.title));
        break;
    }

    _filteredProducts.value = filtered;
  }

  /// Change sort option
  void changeSortOption(SortOption option) {
    sortOption = option;
    _applyFiltersAndSort();
  }

  /// Clear search
  void clearSearch() {
    searchController.clear();
    _applyFiltersAndSort();
  }

  /// Refresh data
  @override
  Future<void> refresh() async {
    if (wholesaler != null) {
      await Future.wait([
        loadWholesalerProducts(wholesaler!.id.toString()),
        loadRegionMinimumCharge(wholesaler!.id.toString()),
      ]);
    }
  }

  /// Navigate to product details
  void navigateToProductDetails(ItemOut product) {
    Get.to(
      () => const ProductDetailsPage(),
      arguments: {'product': product.product},
    );
  }
}
