import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:tagerplus/core/api/api.dart';
import 'package:tagerplus/core/constants/app_colors.dart';
import 'package:tagerplus/core/constants/app_defaults.dart';

import 'package:tagerplus/components/product_tile_square.dart';
import 'package:tagerplus/pages/wholesaler_details/controller.dart';
import 'package:tagerplus/pages/wholesaler_details/components/components.dart';

class WholesalerDetailsPage extends StatelessWidget {
  const WholesalerDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(WholesalerDetailsController());

    return Scaffold(
      appBar: AppBar(
        title: Obx(
          () => Text(controller.wholesaler?.title ?? 'wholesaler_details'.tr),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () => _showSortOptions(context, controller),
            icon: const Icon(Icons.sort),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading && controller.products.isEmpty) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.errorMessage.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  controller.errorMessage,
                  style: Theme.of(context).textTheme.titleMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: controller.refresh,
                  child: Text('retry'.tr),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: controller.refresh,
          child: ListView(
            children: [
              // Wholesaler Header
              if (controller.wholesaler != null)
                WholesalerHeaderSection(wholesaler: controller.wholesaler!),

              // Search Bar
              SearchBarSection(controller: controller),

              // Category Filter Chips
              CategoryFilterChips(controller: controller),

              // Products Grid
              _buildProductsGrid(context, controller),
            ],
          ),
        );
      }),
      bottomNavigationBar: Obx(() => _buildBottomBar(context, controller)),
    );
  }

  Widget _buildProductsGrid(
    BuildContext context,
    WholesalerDetailsController controller,
  ) {
    if (controller.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (controller.filteredProducts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'no_products_found'.tr,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            if (controller.searchController.text.isNotEmpty) ...[
              const SizedBox(height: 8),
              TextButton(
                onPressed: controller.clearSearch,
                child: Text('مسح البحث'),
              ),
            ],
          ],
        ),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.all(AppDefaults.padding),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: 16,
        crossAxisSpacing: 16,
        childAspectRatio: 0.6,
      ),
      itemCount: controller.filteredProducts.length,
      itemBuilder: (context, index) {
        final product = controller.filteredProducts[index];
        return ProductTileSquare(
          product: ProductOut(
            id: product.product.id,
            name: product.product.name,
            title: product.product.title,
            barcode: product.product.barcode,
            slug: product.product.slug,
            description: product.product.description,
            unit: product.product.unit,
            unitCount: product.product.unitCount,
            itemsCount: 0,
            createdAt: '',
            updatedAt: '',
            imageUrl: product.product.imageUrl,
          ),
          onTap: () => controller.navigateToProductDetails(product),
        );
      },
    );
  }

  Widget _buildBottomBar(
    BuildContext context,
    WholesalerDetailsController controller,
  ) {
    // if (controller.regionMinimumCharge <= 0) {
    //   return const SizedBox.shrink();
    // }

    return Container(
      padding: const EdgeInsets.all(AppDefaults.padding),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: AppColors.primary, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '${'region_minimum_charge'.tr}: ${controller.regionMinimumCharge.toStringAsFixed(2)} ${'currency'.tr}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showSortOptions(
    BuildContext context,
    WholesalerDetailsController controller,
  ) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppDefaults.padding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'sort_by'.tr,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            ListTile(
              leading: const Icon(Icons.sort_by_alpha),
              title: Text('sort_by_name'.tr),
              trailing: controller.sortOption == SortOption.name
                  ? Icon(Icons.check, color: AppColors.primary)
                  : null,
              onTap: () {
                controller.changeSortOption(SortOption.name);
                Get.back();
              },
            ),

            ListTile(
              leading: const Icon(Icons.attach_money),
              title: Text('sort_by_price'.tr),
              trailing: controller.sortOption == SortOption.price
                  ? Icon(Icons.check, color: AppColors.primary)
                  : null,
              onTap: () {
                controller.changeSortOption(SortOption.price);
                Get.back();
              },
            ),
          ],
        ),
      ),
    );
  }
}
