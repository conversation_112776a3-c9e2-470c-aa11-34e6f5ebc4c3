import 'package:get/get.dart';

class LocalizationService extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {
    'ar': {
      // Login Page
      'welcome_back': 'أهلاً بعودتك',
      'sign_in_to_continue': 'سجل الدخول للمتابعة',
      'phone_number': 'رقم الهاتف',
      'password': 'كلمة المرور',
      'forgot_password': 'نسيت كلمة المرور؟',
      'sign_in': 'تسجيل الدخول',
      'dont_have_account': 'ليس لديك حساب؟ ',
      'sign_up': 'إنشاء حساب',

      // Signup Page
      'create_account': 'إنشاء حساب جديد',
      'join_us_today': 'انضم إلينا اليوم',
      'full_name': 'الاسم الكامل',
      'confirm_password': 'تأكيد كلمة المرور',
      'agree_to_terms': 'أوافق على الشروط والأحكام',
      'create_account_btn': 'إنشاء الحساب',
      'already_have_account': 'لديك حساب بالفعل؟ ',

      // Validation Messages
      'please_enter_phone': 'يرجى إدخال رقم الهاتف',
      'enter_valid_phone': 'أدخل رقم هاتف صحيح',
      'please_enter_password': 'يرجى إدخال كلمة المرور',
      'password_min_length': 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
      'please_enter_name': 'يرجى إدخال الاسم',
      'name_too_short': 'الاسم قصير جداً',
      'please_confirm_password': 'يرجى تأكيد كلمة المرور',
      'passwords_dont_match': 'كلمات المرور غير متطابقة',
      'please_agree_terms': 'يرجى الموافقة على الشروط والأحكام',

      // Messages
      'success': 'نجح',
      'error': 'خطأ',
      'logged_in_successfully': 'تم تسجيل الدخول بنجاح',
      'login_failed': 'فشل تسجيل الدخول',
      'account_created_successfully': 'تم إنشاء الحساب بنجاح',
      'signup_failed': 'فشل إنشاء الحساب',
      'loading': 'جاري التحميل...',
      'network_error': 'خطأ في الاتصال بالشبكة',
      'server_error': 'خطأ في الخادم',
      'invalid_credentials': 'بيانات الدخول غير صحيحة',
      'phone_already_exists': 'رقم الهاتف مسجل مسبقاً',
      'weak_password': 'كلمة المرور ضعيفة',
      'try_again': 'حاول مرة أخرى',

      // Store Creation
      'create_store': 'إنشاء متجر',
      'store_name': 'اسم المتجر',
      'store_description': 'وصف المتجر',
      'store_address': 'عنوان المتجر',
      'select_region': 'اختر المنطقة',
      'please_enter_store_name': 'يرجى إدخال اسم المتجر',
      'please_enter_store_address': 'يرجى إدخال عنوان المتجر',
      'please_select_region': 'يرجى اختيار المنطقة',
      'store_created_successfully': 'تم إنشاء المتجر بنجاح',
      'store_creation_failed': 'فشل إنشاء المتجر',
      'skip_for_now': 'تخطي الآن',
      'continue': 'متابعة',

      // Navigation
      'welcome_to_app': 'مرحباً بك في تاجر بلس',
      'lets_create_store': 'لننشئ متجرك الأول',
      'store_description_hint': 'وصف قصير عن متجرك (اختياري)',
      'address_too_short': 'العنوان قصير جداً',

      // Home Page
      'home': 'الرئيسية',
      'search': 'البحث',
      'saved': 'المحفوظات',
      'profile': 'الملف الشخصي',
      'cart': 'السلة',
      'view_all': 'عرض الكل',
      'wholesalers': 'التجار',
      'popular_products': 'المنتجات الشائعة',
      'new_products': 'المنتجات الجديدة',
      'latest_products': 'أحدث المنتجات',
      'no_wholesalers_available': 'لا توجد تجار متاحون',
      'wholesalers_coming_soon': 'سيتم إضافة التجار قريباً',
      'loading_wholesalers': 'جاري تحميل التجار...',
      'error_loading_wholesalers': 'حدث خطأ في تحميل التجار',
      'retry': 'إعادة المحاولة',
      'back': 'رجوع',
      'product_details': 'تفاصيل المنتج',
      'product_details_coming_soon': 'تفاصيل المنتج قريباً',
      'price_comparison': 'مقارنة الأسعار',
      'available_from': 'متوفر من',
      'wholesaler': 'التاجر',
      'price': 'السعر',
      'add_to_cart': 'إضافة للسلة',
      'out_of_stock': 'غير متوفر',
      'minimum_order': 'الحد الأدنى للطلب',
      'product_description': 'وصف المنتج',
      'no_description_available': 'لا يوجد وصف متاح',
      'wholesaler_details': 'تفاصيل التاجر',
      'products': 'المنتجات',
      'search_in_products': 'البحث في المنتجات...',
      'filter_products': 'تصفية المنتجات',
      'sort_by': 'ترتيب حسب',
      'sort_by_name': 'الاسم',
      'sort_by_price': 'السعر',
      'region_minimum_charge': 'الحد الأدنى للمنطقة',
      'no_products_found': 'لم يتم العثور على منتجات',
      'loading_products': 'جاري تحميل المنتجات...',
      'all_categories': 'جميع الفئات',
      'beverages': 'المشروبات',
      'snacks': 'الوجبات الخفيفة',
      'dairy': 'منتجات الألبان',
      'frozen': 'المجمدات',
      'bakery': 'المخبوزات',
      'meat': 'اللحوم',
      'vegetables': 'الخضروات',
      'fruits': 'الفواكه',
      'cleaning': 'منتجات التنظيف',
      'personal_care': 'العناية الشخصية',
      'price_not_available': 'السعر غير متوفر',
      'contact_for_price': 'اتصل للسعر',
      'currency': 'ج.م',

      // Search Page
      'search_products': 'ابحث عن المنتجات...',
      'search_suggestions': 'اقتراحات البحث',
      'searching': 'جاري البحث...',
      'no_results_found': 'لم يتم العثور على نتائج',
      'try_different_keywords': 'جرب البحث بكلمات مختلفة',
      'products_found': 'تم العثور على {count} منتج',
      'search_error': 'حدث خطأ أثناء البحث',
      'search_error_retry': 'حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.',
    },
    'en': {
      // Login Page
      'welcome_back': 'Welcome back',
      'sign_in_to_continue': 'Sign in to continue',
      'phone_number': 'Phone number',
      'password': 'Password',
      'forgot_password': 'Forgot password?',
      'sign_in': 'Sign in',
      'dont_have_account': "Don't have an account? ",
      'sign_up': 'Sign up',

      // Signup Page
      'create_account': 'Create Account',
      'join_us_today': 'Join us today',
      'full_name': 'Full Name',
      'confirm_password': 'Confirm Password',
      'agree_to_terms': 'I agree to Terms & Conditions',
      'create_account_btn': 'Create Account',
      'already_have_account': 'Already have an account? ',

      // Validation Messages
      'please_enter_phone': 'Please enter your phone number',
      'enter_valid_phone': 'Enter a valid phone number',
      'please_enter_password': 'Please enter your password',
      'password_min_length': 'Password must be at least 6 characters',
      'please_enter_name': 'Please enter your name',
      'name_too_short': 'Name is too short',
      'please_confirm_password': 'Please confirm your password',
      'passwords_dont_match': 'Passwords do not match',
      'please_agree_terms': 'Please agree to Terms & Conditions',

      // Messages
      'success': 'Success',
      'error': 'Error',
      'logged_in_successfully': 'Logged in successfully',
      'login_failed': 'Login failed',
      'account_created_successfully': 'Account created successfully',
      'signup_failed': 'Signup failed',
      'loading': 'Loading...',
      'network_error': 'Network connection error',
      'server_error': 'Server error',
      'invalid_credentials': 'Invalid credentials',
      'phone_already_exists': 'Phone number already exists',
      'weak_password': 'Password is too weak',
      'try_again': 'Try again',

      // Store Creation
      'create_store': 'Create Store',
      'store_name': 'Store Name',
      'store_description': 'Store Description',
      'store_address': 'Store Address',
      'select_region': 'Select Region',
      'please_enter_store_name': 'Please enter store name',
      'please_enter_store_address': 'Please enter store address',
      'please_select_region': 'Please select a region',
      'store_created_successfully': 'Store created successfully',
      'store_creation_failed': 'Store creation failed',
      'skip_for_now': 'Skip for now',
      'continue': 'Continue',

      // Navigation
      'welcome_to_app': 'Welcome to TagerPlus',
      'lets_create_store': 'Let\'s create your first store',
      'store_description_hint': 'Short description about your store (optional)',
      'address_too_short': 'Address is too short',

      // Home Page
      'home': 'Home',
      'search': 'Search',
      'saved': 'Saved',
      'profile': 'Profile',
      'cart': 'Cart',
      'view_all': 'View All',
      'wholesalers': 'Wholesalers',
      'popular_products': 'Popular Products',
      'new_products': 'New Products',
      'latest_products': 'Latest Products',
      'no_wholesalers_available': 'No wholesalers available',
      'wholesalers_coming_soon': 'Wholesalers will be added soon',
      'loading_wholesalers': 'Loading wholesalers...',
      'error_loading_wholesalers': 'Error loading wholesalers',
      'retry': 'Retry',
      'back': 'Back',
      'product_details': 'Product Details',
      'product_details_coming_soon': 'Product details coming soon',
      'price_comparison': 'Price Comparison',
      'available_from': 'Available from',
      'wholesaler': 'Wholesaler',
      'price': 'Price',
      'add_to_cart': 'Add to Cart',
      'out_of_stock': 'Out of Stock',
      'minimum_order': 'Minimum Order',
      'product_description': 'Product Description',
      'no_description_available': 'No description available',
      'wholesaler_details': 'Wholesaler Details',
      'products': 'Products',
      'search_in_products': 'Search in products...',
      'filter_products': 'Filter Products',
      'sort_by': 'Sort by',
      'sort_by_name': 'Name',
      'sort_by_price': 'Price',
      'region_minimum_charge': 'Region Minimum Charge',
      'no_products_found': 'No products found',
      'loading_products': 'Loading products...',
      'all_categories': 'All Categories',
      'beverages': 'Beverages',
      'snacks': 'Snacks',
      'dairy': 'Dairy',
      'frozen': 'Frozen',
      'bakery': 'Bakery',
      'meat': 'Meat',
      'vegetables': 'Vegetables',
      'fruits': 'Fruits',
      'cleaning': 'Cleaning',
      'personal_care': 'Personal Care',
      'price_not_available': 'Price not available',
      'contact_for_price': 'Contact for price',
      'currency': 'EGP',

      // Search Page
      'search_products': 'Search for products...',
      'search_suggestions': 'Search Suggestions',
      'searching': 'Searching...',
      'no_results_found': 'No results found',
      'try_different_keywords': 'Try searching with different keywords',
      'products_found': 'Found {count} products',
      'search_error': 'Search error',
      'search_error_retry':
          'An error occurred while searching. Please try again.',
    },
  };
}
